#!/bin/bash

# ==============================================================================
# Skrip untuk Menonaktifkan (Disable) Akun <PERSON> di Marzban
# ==============================================================================
#
# Cara Penggunaan:
# 1. Pastikan Anda memiliki file 'domain.txt' dan 'token.txt' di direktori
#    yang sama dengan skrip ini.
#    - domain.txt: Berisi nama domain server Marzban Anda (contoh: sub.domain.com)
#    - token.txt: Berisi access token API Marzban Anda.
# 2. Berikan izin eksekusi pada skrip ini: chmod +x disable_user.sh
# 3. Jalankan skrip dengan nama pengguna sebagai argumen:
#    ./disable_user.sh PENGGUNA_YANG_AKAN_DINONAKTIFKAN
#
# Contoh:
# ./disable_user.sh user-test-123
#
# ==============================================================================

# Periksa apakah nama pengguna diberikan
if [ -z "$1" ]; then
  echo "Kesalahan: Nama pengguna diperlukan."
  echo "Contoh: $0 nama_pengguna"
  exit 1
fi

# Periksa apakah file konfigurasi ada
if [ ! -f "domain.txt" ] || [ ! -f "token.txt" ]; then
    echo "Kesalahan: Pastikan file 'domain.txt' dan 'token.txt' ada di direktori yang sama."
    exit 1
fi

# Baca konfigurasi
DOMAIN=$(cat domain.txt)
TOKEN=$(cat token.txt)
USERNAME=$1

echo "Mencoba menonaktifkan pengguna: ${USERNAME} di server ${DOMAIN}..."

# Kirim permintaan ke API Marzban untuk menonaktifkan akun
RESPONSE_CODE=$(curl -k -s -o /dev/null -w "%{http_code}" -X PUT \
  "https://${DOMAIN}/api/user/${USERNAME}" \
  -H "accept: application/json" \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Content-Type: application/json" \
  -d '{
  "status": "disabled"
}')

# Periksa hasil respons
if [ "$RESPONSE_CODE" -eq 200 ]; then
  echo "Berhasil! Pengguna '${USERNAME}' telah dinonaktifkan."
  printf "1"
else
  echo "Gagal. Server Marzban merespons dengan kode status: ${RESPONSE_CODE}"
  echo "Pastikan domain, token, dan nama pengguna sudah benar."
  printf "0"
fi
