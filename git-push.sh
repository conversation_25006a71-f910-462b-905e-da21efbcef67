#!/bin/bash

# Hentikan skrip jika ada perintah yang gagal
set -e

# --- Dapatkan Nama Branch Saat Ini ---
CURRENT_BRANCH=$(git rev-parse --abbrev-ref HEAD)
if [ -z "$CURRENT_BRANCH" ]; then
    echo "❌ Error: Tidak dapat mendeteksi branch Git. Apakah Anda berada di dalam sebuah repo Git?"
    exit 1
fi

echo "🚀 Memulai proses push untuk branch: $CURRENT_BRANCH"

# 1. Tambahkan semua perubahan ke staging area
echo "- Menjalankan git add ."
git add .

# 2. <PERSON><PERSON><PERSON> pesan commit dengan tanggal dan waktu saat ini
COMMIT_MSG="feat: Auto-commit on $(date +'%Y-%m-%d %H:%M:%S')"
echo "- Membuat commit dengan pesan: '$COMMIT_MSG'"
git commit -m "$COMMIT_MSG"

# 3. Push ke remote 'origin' dengan branch saat ini
echo "- Menjalankan git push origin $CURRENT_BRANCH"
git push origin "$CURRENT_BRANCH"

echo "✅  Proses selesai. Perubahan berhasil di-push ke origin/$CURRENT_BRANCH."
