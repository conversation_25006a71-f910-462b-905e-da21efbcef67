#!/bin/bash

# --- Konfigurasi ---
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpbnNvbW5pYSIsImFjY2VzcyI6InN1ZG8iLCJpYXQiOjE3NTEzMDUzMTB9.dTHWIkOQipqC00i2cgJuYiJnfJUhut1GwAt3Wj9oxtU"
API_URL="https://ggl.koneksiku.me/api/user"

# --- Logika Argumen ---
if [ -n "$1" ]; then
  BASE_USERNAME="$1"
else
  BASE_USERNAME="test_user_$(date +%s)"
fi

# --- Pengaturan Umum ---
EXPIRE_TIMESTAMP=$(date -d "+30 days" +%s)
DATA_LIMIT=$((1000 * 1024 * 1024 * 1024))

# --- Fungsi untuk mengirim permintaan ---
# Argumen: 1. <PERSON><PERSON>, 2. Payload JSON
create_user() {
  local PROTOCOL_NAME=$1
  local JSON_PAYLOAD=$2
  local USERNAME=$(echo "$JSON_PAYLOAD" | jq -r .username)

  echo "--- Membuat Pengguna ${PROTOCOL_NAME} (30 Hari) ---"
  echo "Username: ${USERNAME}"
  echo "--------------------------------"
  echo "Mengirim permintaan untuk membuat pengguna..."

  HTTP_RESPONSE=$(curl --silent --write-out "HTTPSTATUS:%{http_code}" -X POST \
    -H "Authorization: Bearer ${TOKEN}" \
    -H "Content-Type: application/json" \
    -H "Accept: application/json" \
    -d "${JSON_PAYLOAD}" \
    "${API_URL}")

  HTTP_BODY=$(echo "$HTTP_RESPONSE" | sed -e 's/HTTPSTATUS:.*//g')
  HTTP_STATUS=$(echo "$HTTP_RESPONSE" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')

  echo "Status HTTP: ${HTTP_STATUS}"
  echo "Respons Body:"
  if command -v jq &> /dev/null; then
      echo "$HTTP_BODY" | jq .
  else
      echo "$HTTP_BODY"
  fi

  if [ "$HTTP_STATUS" -eq 200 ]; then
    echo -e "\nPengguna ${PROTOCOL_NAME} '\033[1;32m${USERNAME}\033[0m' berhasil dibuat."
  else
    echo -e "\n\033[1;31mGagal membuat pengguna ${PROTOCOL_NAME}.\033[0m"
  fi
  echo "================================================"
}

# --- 1. Membuat Pengguna Trojan ---
TROJAN_USERNAME="${BASE_USERNAME}_trojan"
TROJAN_PASSWORD=$(uuidgen)
read -r -d '' TROJAN_PAYLOAD << EOM
{
  "username": "${TROJAN_USERNAME}",
  "proxies": { "trojan": { "password": "${TROJAN_PASSWORD}" } },
  "inbounds": { "trojan": [] },
  "expire": ${EXPIRE_TIMESTAMP},
  "data_limit": ${DATA_LIMIT},
  "data_limit_reset_strategy": "month"
}
EOM
create_user "Trojan" "${TROJAN_PAYLOAD}"

# --- 2. Membuat Pengguna Vmess ---
VMESS_USERNAME="${BASE_USERNAME}_vmess"
VMESS_UUID=$(uuidgen)
read -r -d '' VMESS_PAYLOAD << EOM
{
  "username": "${VMESS_USERNAME}",
  "proxies": { "vmess": { "id": "${VMESS_UUID}" } },
  "inbounds": { "vmess": [] },
  "expire": ${EXPIRE_TIMESTAMP},
  "data_limit": ${DATA_LIMIT},
  "data_limit_reset_strategy": "month"
}
EOM
create_user "Vmess" "${VMESS_PAYLOAD}"

# --- 3. Membuat Pengguna Vless ---
VLESS_USERNAME="${BASE_USERNAME}_vless"
VLESS_UUID=$(uuidgen)
read -r -d '' VLESS_PAYLOAD << EOM
{
  "username": "${VLESS_USERNAME}",
  "proxies": { "vless": { "id": "${VLESS_UUID}", "flow": "xtls-rprx-vision" } },
  "inbounds": { "vless": [] },
  "expire": ${EXPIRE_TIMESTAMP},
  "data_limit": ${DATA_LIMIT},
  "data_limit_reset_strategy": "month"
}
EOM
create_user "Vless" "${VLESS_PAYLOAD}"
