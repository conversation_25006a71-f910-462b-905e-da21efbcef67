-- <PERSON>ript untuk mengisi data demo yang banyak
-- Untuk keperluan demonstrasi aplikasi VPN Shop

-- Hapus data lama jika ada (opsional)
-- DELETE FROM account_trojans;
-- DELETE FROM account_vmess;
-- DELETE FROM account_vless;
-- DELETE FROM transactions;
-- DELETE FROM announcements;
-- DELETE FROM servers;
-- DELETE FROM users WHERE id > 1; -- <PERSON><PERSON> hapus admin utama

-- 1. INSERT DATA USERS (50 users)
INSERT INTO users (name, username, email, password, saldo, verif_wa, whatsapp, batas_trial, trial, pay_harian, pay_mingguan, pay_bulanan, pay_perjam, total_pay, email_verified, created_at, updated_at) VALUES
('<PERSON> Rizki', 'ahmad_rizki', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 150000, 1, '************', 5, 2, 0, 0, 250000, 0, 250000, 1, NOW(), NOW()),
('Siti Nurhaliza', 'siti_nur', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 75000, 1, '************', 5, 1, 0, 0, 150000, 0, 150000, 1, NOW(), NOW()),
('Budi Santoso', 'budi_santoso', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 300000, 1, '************', 5, 0, 0, 0, 500000, 0, 500000, 1, NOW(), NOW()),
('Dewi Lestari', 'dewi_lestari', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 200000, 1, '081234567893', 5, 3, 0, 0, 300000, 0, 300000, 1, NOW(), NOW()),
('Eko Prasetyo', 'eko_prasetyo', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 125000, 1, '081234567894', 5, 1, 0, 0, 200000, 0, 200000, 1, NOW(), NOW()),
('Fitri Handayani', 'fitri_handayani', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 180000, 1, '081234567895', 5, 2, 0, 0, 280000, 0, 280000, 1, NOW(), NOW()),
('Gunawan Setiawan', 'gunawan_setiawan', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 90000, 1, '081234567896', 5, 4, 0, 0, 120000, 0, 120000, 1, NOW(), NOW()),
('Hani Rahmawati', 'hani_rahmawati', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 250000, 1, '081234567897', 5, 0, 0, 0, 400000, 0, 400000, 1, NOW(), NOW()),
('Indra Kurniawan', 'indra_kurniawan', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 320000, 1, '081234567898', 5, 1, 0, 0, 450000, 0, 450000, 1, NOW(), NOW()),
('Joko Widodo', 'joko_widodo', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 100000, 1, '081234567899', 5, 2, 0, 0, 180000, 0, 180000, 1, NOW(), NOW());

-- Tambah 40 user lagi dengan data yang bervariasi
INSERT INTO users (name, username, email, password, saldo, verif_wa, whatsapp, batas_trial, trial, pay_harian, pay_mingguan, pay_bulanan, pay_perjam, total_pay, email_verified, created_at, updated_at) 
SELECT 
    'User ' || generate_series(11, 50),
    'user_' || generate_series(11, 50),
    'user' || generate_series(11, 50) || '@email.com',
    '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi',
    (RANDOM() * 500000)::INTEGER,
    1,
    '0812345678' || LPAD(generate_series(11, 50)::TEXT, 2, '0'),
    5,
    (RANDOM() * 5)::INTEGER,
    0,
    0,
    (RANDOM() * 1000000)::INTEGER,
    0,
    (RANDOM() * 1000000)::INTEGER,
    1,
    NOW() - INTERVAL '1 day' * (RANDOM() * 30),
    NOW();

-- 2. INSERT DATA SERVERS (15 servers)
INSERT INTO servers (nama, kode, domain, token, negara, nama_isp, harga_member, harga_reseller, ssh, trojan, vmess, vless, slot_server, slot_terpakai, total_user, max_device, created_at, updated_at) VALUES
('Singapore Premium', 'SG-PREM-01', 'sg1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9', 'Singapore', 'DigitalOcean', 25000, 20000, '22', '443', '80', '8080', 100, 45, 45, 2, NOW(), NOW()),
('Singapore Standard', 'SG-STD-01', 'sg2.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ8', 'Singapore', 'Vultr', 20000, 15000, '22', '443', '80', '8080', 80, 32, 32, 2, NOW(), NOW()),
('Japan Tokyo', 'JP-TKY-01', 'jp1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9', 'Japan', 'Linode', 30000, 25000, '22', '443', '80', '8080', 120, 67, 67, 2, NOW(), NOW()),
('USA California', 'US-CA-01', 'us1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ0', 'United States', 'AWS', 35000, 30000, '22', '443', '80', '8080', 150, 89, 89, 2, NOW(), NOW()),
('Germany Frankfurt', 'DE-FRA-01', 'de1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ1', 'Germany', 'Hetzner', 28000, 23000, '22', '443', '80', '8080', 100, 54, 54, 2, NOW(), NOW()),
('Netherlands Amsterdam', 'NL-AMS-01', 'nl1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ2', 'Netherlands', 'DigitalOcean', 26000, 21000, '22', '443', '80', '8080', 90, 41, 41, 2, NOW(), NOW()),
('UK London', 'UK-LON-01', 'uk1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ3', 'United Kingdom', 'Vultr', 32000, 27000, '22', '443', '80', '8080', 110, 63, 63, 2, NOW(), NOW()),
('Australia Sydney', 'AU-SYD-01', 'au1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ4', 'Australia', 'Linode', 33000, 28000, '22', '443', '80', '8080', 85, 38, 38, 2, NOW(), NOW()),
('Canada Toronto', 'CA-TOR-01', 'ca1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ5', 'Canada', 'DigitalOcean', 29000, 24000, '22', '443', '80', '8080', 95, 47, 47, 2, NOW(), NOW()),
('France Paris', 'FR-PAR-01', 'fr1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ6', 'France', 'OVH', 27000, 22000, '22', '443', '80', '8080', 105, 52, 52, 2, NOW(), NOW()),
('Hong Kong', 'HK-HKG-01', 'hk1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ7', 'Hong Kong', 'Vultr', 31000, 26000, '22', '443', '80', '8080', 75, 34, 34, 2, NOW(), NOW()),
('South Korea Seoul', 'KR-SEL-01', 'kr1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ8', 'South Korea', 'Naver Cloud', 34000, 29000, '22', '443', '80', '8080', 80, 36, 36, 2, NOW(), NOW()),
('India Mumbai', 'IN-MUM-01', 'in1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9', 'India', 'DigitalOcean', 22000, 17000, '22', '443', '80', '8080', 120, 71, 71, 2, NOW(), NOW()),
('Brazil Sao Paulo', 'BR-SAO-01', 'br1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJa', 'Brazil', 'AWS', 30000, 25000, '22', '443', '80', '8080', 70, 29, 29, 2, NOW(), NOW()),
('Thailand Bangkok', 'TH-BKK-01', 'th1.insomvpn.com', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJb', 'Thailand', 'Vultr', 24000, 19000, '22', '443', '80', '8080', 90, 43, 43, 2, NOW(), NOW());

-- 3. INSERT DATA ANNOUNCEMENTS (20 announcements)
INSERT INTO announcements (judul, isi, created_at, updated_at) VALUES
('Selamat Datang di InsomVPN!', 'Terima kasih telah bergabung dengan InsomVPN. Nikmati koneksi internet yang cepat dan aman dengan server premium kami di seluruh dunia.', NOW() - INTERVAL '30 days', NOW()),
('Server Baru: Singapore Premium', 'Kami telah menambahkan server baru di Singapore dengan performa premium. Cocok untuk streaming dan gaming dengan ping rendah.', NOW() - INTERVAL '25 days', NOW()),
('Maintenance Terjadwal', 'Akan ada maintenance pada server Japan Tokyo pada tanggal 15 Januari 2024 pukul 02:00 - 04:00 WIB. Mohon maaf atas ketidaknyamanannya.', NOW() - INTERVAL '20 days', NOW()),
('Promo Spesial Tahun Baru!', 'Dapatkan diskon 50% untuk pembelian paket bulanan selama bulan Januari 2024. Gunakan kode: NEWYEAR2024', NOW() - INTERVAL '18 days', NOW()),
('Update Aplikasi v2.1.0', 'Aplikasi InsomVPN telah diperbarui dengan fitur-fitur baru: Auto-reconnect, Kill Switch, dan Split Tunneling. Silakan update aplikasi Anda.', NOW() - INTERVAL '15 days', NOW()),
('Server USA California Upgraded', 'Server USA California telah di-upgrade dengan bandwidth yang lebih besar untuk performa yang lebih optimal.', NOW() - INTERVAL '12 days', NOW()),
('Tips Keamanan Internet', 'Selalu gunakan VPN saat terhubung ke WiFi publik. InsomVPN melindungi data pribadi Anda dengan enkripsi tingkat militer.', NOW() - INTERVAL '10 days', NOW()),
('Panduan Troubleshooting', 'Jika mengalami masalah koneksi, coba ganti server atau restart aplikasi. Tim support kami siap membantu 24/7.', NOW() - INTERVAL '8 days', NOW()),
('Server Baru: Germany Frankfurt', 'Server baru di Frankfurt, Germany telah aktif! Cocok untuk akses konten Eropa dengan kecepatan tinggi.', NOW() - INTERVAL '6 days', NOW()),
('Kebijakan Fair Usage', 'Untuk menjaga kualitas layanan, kami menerapkan kebijakan fair usage. Penggunaan berlebihan dapat dibatasi sementara.', NOW() - INTERVAL '5 days', NOW()),
('Fitur Baru: Multi-Device Support', 'Sekarang Anda dapat menggunakan 1 akun untuk 2 device secara bersamaan. Nikmati fleksibilitas yang lebih besar!', NOW() - INTERVAL '4 days', NOW()),
('Server Maintenance Selesai', 'Maintenance pada server Japan Tokyo telah selesai. Semua layanan kembali normal dengan performa yang lebih baik.', NOW() - INTERVAL '3 days', NOW()),
('Panduan Setup Manual', 'Tersedia panduan setup manual untuk berbagai device di website kami. Cocok untuk pengguna advanced yang ingin kustomisasi.', NOW() - INTERVAL '2 days', NOW()),
('Server Monitoring Real-time', 'Kami telah mengimplementasikan monitoring real-time untuk semua server. Status server dapat dilihat di dashboard.', NOW() - INTERVAL '1 day', NOW()),
('Promo Extend: Diskon 30%', 'Promo tahun baru diperpanjang! Dapatkan diskon 30% untuk semua paket dengan kode: EXTEND30', NOW() - INTERVAL '12 hours', NOW()),
('Server Load Balancing', 'Sistem load balancing otomatis telah diaktifkan untuk distribusi traffic yang lebih merata antar server.', NOW() - INTERVAL '6 hours', NOW()),
('Update Keamanan Penting', 'Patch keamanan terbaru telah diterapkan pada semua server. Tidak ada downtime yang diperlukan.', NOW() - INTERVAL '3 hours', NOW()),
('Feedback dan Saran', 'Kami sangat menghargai feedback dari pengguna. Kirimkan saran Anda melalui support ticket atau email.', NOW() - INTERVAL '2 hours', NOW()),
('Server Performance Report', 'Laporan performa server bulan ini menunjukkan uptime 99.9% dengan rata-rata ping <50ms untuk semua lokasi.', NOW() - INTERVAL '1 hour', NOW()),
('Terima Kasih atas Kepercayaan', 'Terima kasih telah mempercayai InsomVPN sebagai provider VPN pilihan Anda. Kami berkomitmen memberikan layanan terbaik.', NOW() - INTERVAL '30 minutes', NOW());

-- 4. INSERT DATA TRANSACTIONS (100 transactions)
-- Ambil user_id yang valid terlebih dahulu
WITH user_ids AS (
    SELECT id FROM users ORDER BY id LIMIT 50
),
transaction_data AS (
    SELECT 
        'INV-' || TO_CHAR(NOW() - INTERVAL '1 day' * (RANDOM() * 30), 'YYYYMMDD') || '-' || LPAD((ROW_NUMBER() OVER())::TEXT, 6, '0') as invoice_id,
        u.id as user_id,
        CASE 
            WHEN RANDOM() < 0.3 THEN 'PURCHASE_MONTHLY'
            WHEN RANDOM() < 0.6 THEN 'PURCHASE_HOURLY'
            WHEN RANDOM() < 0.8 THEN 'TOPUP'
            WHEN RANDOM() < 0.9 THEN 'RENEWAL'
            ELSE 'TRIAL'
        END as type,
        CASE 
            WHEN RANDOM() < 0.7 THEN 'PAID'
            WHEN RANDOM() < 0.9 THEN 'PENDING'
            ELSE 'FAILED'
        END as status,
        (RANDOM() * 500000 + 10000)::INTEGER as amount,
        NOW() - INTERVAL '1 day' * (RANDOM() * 30) as created_at
    FROM user_ids u
    CROSS JOIN generate_series(1, 2) -- 2 transaksi per user = 100 transaksi
)
INSERT INTO transactions (invoice_id, user_id, type, description, amount, status, payment_gateway, gateway_reference, created_at, updated_at)
SELECT 
    invoice_id,
    user_id,
    type::varchar,
    CASE type
        WHEN 'PURCHASE_MONTHLY' THEN 'Pembelian Paket Bulanan VPN'
        WHEN 'PURCHASE_HOURLY' THEN 'Pembelian Paket Per Jam VPN'
        WHEN 'TOPUP' THEN 'Top Up Saldo'
        WHEN 'RENEWAL' THEN 'Perpanjangan Layanan VPN'
        ELSE 'Trial Gratis VPN'
    END,
    amount,
    status::varchar,
    CASE 
        WHEN RANDOM() < 0.4 THEN 'tripay'
        WHEN RANDOM() < 0.7 THEN 'midtrans'
        ELSE 'manual'
    END,
    'REF-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 10)),
    created_at,
    created_at + INTERVAL '5 minutes'
FROM transaction_data;

-- 5. INSERT DATA ACCOUNT_TROJANS (80 accounts)
WITH server_codes AS (
    SELECT kode FROM servers ORDER BY RANDOM() LIMIT 10
),
user_data AS (
    SELECT id::TEXT as user_id FROM users ORDER BY RANDOM() LIMIT 40
)
INSERT INTO account_trojans (user_id, kode_server, kode_akun, domain, durasi, username, uuid, expired, tanggal_beli, status, is_hourly, subscription_type, harga, created_at, updated_at)
SELECT 
    u.user_id,
    s.kode,
    'TRJ-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 8)),
    CASE s.kode
        WHEN 'SG-PREM-01' THEN 'sg1.insomvpn.com'
        WHEN 'SG-STD-01' THEN 'sg2.insomvpn.com'
        WHEN 'JP-TKY-01' THEN 'jp1.insomvpn.com'
        WHEN 'US-CA-01' THEN 'us1.insomvpn.com'
        WHEN 'DE-FRA-01' THEN 'de1.insomvpn.com'
        ELSE s.kode || '.insomvpn.com'
    END,
    CASE 
        WHEN RANDOM() < 0.7 THEN '30 hari'
        WHEN RANDOM() < 0.9 THEN '7 hari'
        ELSE '1 hari'
    END,
    'trojan-' || LOWER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 6)),
    UPPER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 8) || '-' || SUBSTRING(MD5(RANDOM()::TEXT), 1, 4) || '-' || SUBSTRING(MD5(RANDOM()::TEXT), 1, 4) || '-' || SUBSTRING(MD5(RANDOM()::TEXT), 1, 12)),
    NOW() + INTERVAL '30 days' - INTERVAL '1 day' * (RANDOM() * 60),
    NOW() - INTERVAL '1 day' * (RANDOM() * 30),
    CASE 
        WHEN RANDOM() < 0.8 THEN 'active'
        WHEN RANDOM() < 0.95 THEN 'expired'
        ELSE 'suspended'
    END,
    RANDOM() < 0.3,
    CASE 
        WHEN RANDOM() < 0.7 THEN 'monthly'
        WHEN RANDOM() < 0.9 THEN 'weekly'
        ELSE 'daily'
    END,
    (RANDOM() * 50000 + 15000)::INTEGER::TEXT,
    NOW() - INTERVAL '1 day' * (RANDOM() * 30),
    NOW()
FROM user_data u
CROSS JOIN server_codes s
LIMIT 80;

-- 6. INSERT DATA ACCOUNT_VMESS (75 accounts)
WITH server_codes AS (
    SELECT kode FROM servers ORDER BY RANDOM() LIMIT 10
),
user_data AS (
    SELECT id::TEXT as user_id FROM users ORDER BY RANDOM() LIMIT 35
)
INSERT INTO account_vmess (user_id, kode_server, kode_akun, domain, durasi, username, uuid, expired, tanggal_beli, status, is_hourly, subscription_type, harga, created_at, updated_at)
SELECT 
    u.user_id,
    s.kode,
    'VMS-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 8)),
    CASE s.kode
        WHEN 'SG-PREM-01' THEN 'sg1.insomvpn.com'
        WHEN 'SG-STD-01' THEN 'sg2.insomvpn.com'
        WHEN 'JP-TKY-01' THEN 'jp1.insomvpn.com'
        WHEN 'US-CA-01' THEN 'us1.insomvpn.com'
        WHEN 'DE-FRA-01' THEN 'de1.insomvpn.com'
        ELSE s.kode || '.insomvpn.com'
    END,
    CASE 
        WHEN RANDOM() < 0.7 THEN '30 hari'
        WHEN RANDOM() < 0.9 THEN '7 hari'
        ELSE '1 hari'
    END,
    'vmess-' || LOWER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 6)),
    UPPER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 8) || '-' || SUBSTRING(MD5(RANDOM()::TEXT), 1, 4) || '-' || SUBSTRING(MD5(RANDOM()::TEXT), 1, 4) || '-' || SUBSTRING(MD5(RANDOM()::TEXT), 1, 12)),
    NOW() + INTERVAL '30 days' - INTERVAL '1 day' * (RANDOM() * 60),
    NOW() - INTERVAL '1 day' * (RANDOM() * 30),
    CASE 
        WHEN RANDOM() < 0.8 THEN 'active'
        WHEN RANDOM() < 0.95 THEN 'expired'
        ELSE 'suspended'
    END,
    RANDOM() < 0.3,
    CASE 
        WHEN RANDOM() < 0.7 THEN 'monthly'
        WHEN RANDOM() < 0.9 THEN 'weekly'
        ELSE 'daily'
    END,
    (RANDOM() * 50000 + 15000)::INTEGER::TEXT,
    NOW() - INTERVAL '1 day' * (RANDOM() * 30),
    NOW()
FROM user_data u
CROSS JOIN server_codes s
LIMIT 75;

-- 7. INSERT DATA ACCOUNT_VLESS (70 accounts)
WITH server_codes AS (
    SELECT kode FROM servers ORDER BY RANDOM() LIMIT 10
),
user_data AS (
    SELECT id::TEXT as user_id FROM users ORDER BY RANDOM() LIMIT 30
)
INSERT INTO account_vless (user_id, kode_server, kode_akun, domain, durasi, username, uuid, expired, tanggal_beli, status, is_hourly, subscription_type, harga, created_at, updated_at)
SELECT 
    u.user_id,
    s.kode,
    'VLS-' || UPPER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 8)),
    CASE s.kode
        WHEN 'SG-PREM-01' THEN 'sg1.insomvpn.com'
        WHEN 'SG-STD-01' THEN 'sg2.insomvpn.com'
        WHEN 'JP-TKY-01' THEN 'jp1.insomvpn.com'
        WHEN 'US-CA-01' THEN 'us1.insomvpn.com'
        WHEN 'DE-FRA-01' THEN 'de1.insomvpn.com'
        ELSE s.kode || '.insomvpn.com'
    END,
    CASE 
        WHEN RANDOM() < 0.7 THEN '30 hari'
        WHEN RANDOM() < 0.9 THEN '7 hari'
        ELSE '1 hari'
    END,
    'vless-' || LOWER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 6)),
    UPPER(SUBSTRING(MD5(RANDOM()::TEXT), 1, 8) || '-' || SUBSTRING(MD5(RANDOM()::TEXT), 1, 4) || '-' || SUBSTRING(MD5(RANDOM()::TEXT), 1, 4) || '-' || SUBSTRING(MD5(RANDOM()::TEXT), 1, 12)),
    NOW() + INTERVAL '30 days' - INTERVAL '1 day' * (RANDOM() * 60),
    NOW() - INTERVAL '1 day' * (RANDOM() * 30),
    CASE 
        WHEN RANDOM() < 0.8 THEN 'active'
        WHEN RANDOM() < 0.95 THEN 'expired'
        ELSE 'suspended'
    END,
    RANDOM() < 0.3,
    CASE 
        WHEN RANDOM() < 0.7 THEN 'monthly'
        WHEN RANDOM() < 0.9 THEN 'weekly'
        ELSE 'daily'
    END,
    (RANDOM() * 50000 + 15000)::INTEGER::TEXT,
    NOW() - INTERVAL '1 day' * (RANDOM() * 30),
    NOW()
FROM user_data u
CROSS JOIN server_codes s
LIMIT 70;

-- Tampilkan ringkasan data yang telah diinsert
SELECT 'users' as table_name, COUNT(*) as total_records FROM users
UNION ALL
SELECT 'servers' as table_name, COUNT(*) as total_records FROM servers
UNION ALL
SELECT 'announcements' as table_name, COUNT(*) as total_records FROM announcements
UNION ALL
SELECT 'transactions' as table_name, COUNT(*) as total_records FROM transactions
UNION ALL
SELECT 'account_trojans' as table_name, COUNT(*) as total_records FROM account_trojans
UNION ALL
SELECT 'account_vmess' as table_name, COUNT(*) as total_records FROM account_vmess
UNION ALL
SELECT 'account_vless' as table_name, COUNT(*) as total_records FROM account_vless
ORDER BY table_name;

-- Script selesai
-- Total data yang dihasilkan:
-- - 50+ Users
-- - 15 Servers
-- - 20 Announcements
-- - 100 Transactions
-- - 80 Account Trojans
-- - 75 Account Vmess
-- - 70 Account Vless
-- Total: 410+ records untuk demo yang komprehensif