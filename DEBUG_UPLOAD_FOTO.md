# Debug Upload Foto - "File foto tidak ditemukan"

## 🐛 Masalah yang Ditemukan

Error "File foto tidak ditemukan" terjadi karena **ApiClient secara otomatis mengubah FormData menjadi JSON string**, yang men<PERSON><PERSON><PERSON> backend tidak bisa membaca file yang diupload.

## ✅ Solusi yang Diterapkan

### 1. Perbaikan ApiClient (`frondend/src/lib/apiClientEnhanced.ts`)

**Masalah**: 
```typescript
// SALAH - FormData di-stringify menjadi JSON
body: data ? JSON.stringify(data) : undefined
```

**Solusi**:
```typescript
// BENAR - FormData dikirim langsung tanpa stringify
body: data instanceof FormData ? data : (data ? JSON.stringify(data) : undefined)
```

### 2. Perbaikan Content-Type Header

**Masalah**:
```typescript
// SALAH - Content-Type di-set untuk semua request
if (!headers.has('Content-Type') && restOptions.method !== 'GET') {
  headers.set('Content-Type', 'application/json');
}
```

**Solusi**:
```typescript
// BENAR - Tidak set Content-Type untuk FormData
if (!headers.has('Content-Type') && restOptions.method !== 'GET' && !(restOptions.body instanceof FormData)) {
  headers.set('Content-Type', 'application/json');
}
```

## 🔧 Perubahan yang Dilakukan

### File: `frondend/src/lib/apiClientEnhanced.ts`

1. **Method `post()`** - Line 90-96
2. **Override `post()` dengan token** - Line 139-140  
3. **Override `post()` dengan accessToken** - Line 169-170
4. **Content-Type logic** - Line 42-45

### Penjelasan Teknis

**FormData** adalah objek khusus untuk upload file yang:
- Harus dikirim langsung tanpa JSON.stringify()
- Tidak boleh ada Content-Type header (browser akan set otomatis dengan boundary)
- Menggunakan encoding `multipart/form-data`

## 🧪 Testing

### 1. Test Manual dengan Browser
```javascript
// Buka browser console di halaman profile
const formData = new FormData();
const fileInput = document.querySelector('input[type="file"]');
formData.append('photo', fileInput.files[0]);

fetch('/api/v1/users/me/photo', {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer YOUR_TOKEN'
  },
  body: formData
}).then(r => r.json()).then(console.log);
```

### 2. Test dengan File HTML
Gunakan `frondend/test_formdata.html` untuk test langsung.

### 3. Verifikasi Network Tab
Di browser DevTools → Network:
- Request harus menunjukkan `Content-Type: multipart/form-data; boundary=...`
- Body harus berisi binary data, bukan JSON string

## 🔍 Debugging Tips

### 1. Cek Request di Network Tab
```
Method: POST
URL: /api/v1/users/me/photo
Content-Type: multipart/form-data; boundary=----WebKitFormBoundary...
Authorization: Bearer eyJ...

Body:
------WebKitFormBoundary...
Content-Disposition: form-data; name="photo"; filename="image.jpg"
Content-Type: image/jpeg

[binary data]
------WebKitFormBoundary...
```

### 2. Cek Backend Logs
```bash
cd backend-go
go run main.go

# Di terminal lain, cek logs
tail -f logs/golang.log
```

### 3. Test Backend Langsung
```bash
cd backend-go
./test_upload.sh
```

## ✅ Status Perbaikan

- ✅ **ApiClient**: FormData tidak di-stringify lagi
- ✅ **Content-Type**: Tidak di-set untuk FormData
- ✅ **Backend**: Sudah benar, tidak perlu diubah
- ✅ **Frontend**: ProfilePhotoUpload sudah benar

## 🚀 Cara Test Setelah Perbaikan

1. **Restart Frontend** (jika perlu):
   ```bash
   cd frondend
   npm run dev
   ```

2. **Login** ke aplikasi sebagai admin/admin

3. **Buka Profile** dan coba upload foto

4. **Cek Network Tab** untuk memastikan request benar

5. **Verifikasi** foto tersimpan di `backend-go/uploads/photos/`

## 📝 Expected Result

**Success Response**:
```json
{
  "message": "Foto profil berhasil diupload",
  "photo_url": "uploads/photos/user_1_1752419184.png"
}
```

**File tersimpan** di: `backend-go/uploads/photos/user_1_1752419184.png`

**Foto ditampilkan** di profile page setelah reload.

---

**Masalah "File foto tidak ditemukan" seharusnya sudah teratasi!** 🎉
