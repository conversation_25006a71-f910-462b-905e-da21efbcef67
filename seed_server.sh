#!/bin/bash

# Skrip ini untuk mengisi data awal (seeding) ke database dengan membuat server default.
# Dijalankan dari direktori root project vpn-shop.

set -e # Keluar dari skrip jika ada perintah yang gagal

# --- Val<PERSON>i Awal ---
echo "<PERSON><PERSON><PERSON> proses seeding server..."

# 1. Cek apakah jq terinstal
if ! command -v jq &> /dev/null; then
    echo "Error: 'jq' tidak terinstal. Silakan instal jq untuk melanjutkan."
    echo "Contoh instalasi: sudo apt-get install jq (Debian/Ubuntu) atau brew install jq (macOS)"
    exit 1
fi

# 2. Pastikan server backend ber<PERSON><PERSON> (dengan mencoba koneksi)
if ! curl -s http://localhost:8000/ > /dev/null; then
    echo "Error: Tidak dapat terhubung ke server di http://localhost:8000."
    echo "Pastikan server Go sudah berjalan sebelum menjalankan skrip ini (go run main.go)."
    exit 1
fi


# --- Konfigurasi ---
API_BASE_URL="http://localhost:8000/api/v1"
LOGIN_URL="$API_BASE_URL/auth/login"
SERVER_URL="$API_BASE_URL/servers"


# --- Proses Login ---
echo "Login sebagai admin untuk mendapatkan token..."

# Gunakan --data-urlencode untuk keamanan dan kirim sebagai form
LOGIN_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X POST "$LOGIN_URL" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  --data-urlencode "username=admin" \
  --data-urlencode "password=admin")

# Pisahkan body dan status code
HTTP_BODY=$(echo "$LOGIN_RESPONSE" | sed '$d')
HTTP_STATUS=$(echo "$LOGIN_RESPONSE" | tail -n1 | sed 's/.*HTTP_STATUS://')

if [ "$HTTP_STATUS" -ne 200 ]; then
    echo "  -> Login GAGAL. Server merespon dengan status: $HTTP_STATUS"
    echo "  -> Response Body:"
    echo "$HTTP_BODY"
    exit 1
fi

TOKEN=$(echo "$HTTP_BODY" | jq -r .access_token)

if [ -z "$TOKEN" ] || [ "$TOKEN" == "null" ]; then
    echo "  -> Gagal mengekstrak 'access_token' dari response. Body yang diterima:"
    echo "$HTTP_BODY"
    exit 1
fi

echo "  -> Login berhasil! Token diperoleh."


# --- Proses Pembuatan Server ---
SERVER_DATA=$(cat <<'EOF'
{
  "domain": "ggl.koneksiku.me",
  "harga_member": 10000,
  "harga_reseller": 8000,
  "kode": "gcloud-1",
  "max_device": 1,
  "nama": "GCLOUD-1",
  "nama_isp": "GOOGLE CLOUD",
  "negara": "indonesia",
  "slot_server": 50,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpbnNvbW5pYSIsImFjY2VzcyI6InN1ZG8iLCJpYXQiOjE3NTEzMDUzMTB9.dTHWIkOQipqC00i2cgJuYiJnfJUhut1GwAt3Wj9oxtU",
  "trojan": "enable",
  "vless": "enable",
  "vmess": "enable",
  "ssh": "disable"
}
EOF
)

echo "Membuat atau memperbarui server 'gcloud-1'..."

CREATE_RESPONSE=$(curl -s -w "\nHTTP_STATUS:%{http_code}" -X POST "$SERVER_URL" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $TOKEN" \
  -d "$SERVER_DATA")

# Pisahkan body dan status code
CREATE_HTTP_BODY=$(echo "$CREATE_RESPONSE" | sed '$d')
CREATE_HTTP_STATUS=$(echo "$CREATE_RESPONSE" | tail -n1 | sed 's/.*HTTP_STATUS://')

if [ "$CREATE_HTTP_STATUS" -eq 201 ]; then
    echo "  -> SUKSES: Server 'gcloud-1' berhasil dibuat."
    echo "     Response: $CREATE_HTTP_BODY"
elif [ "$CREATE_HTTP_STATUS" -eq 409 ]; then
    echo "  -> INFO: Server 'gcloud-1' sudah ada. Tidak ada yang diubah."
else
    echo "  -> GAGAL membuat server. Status: $CREATE_HTTP_STATUS"
    echo "     Response: $CREATE_HTTP_BODY"
    exit 1
fi

echo "Proses seeding selesai dengan sukses."
