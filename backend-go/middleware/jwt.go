package middleware

import (
	"fmt"
	"strconv"

	"github.com/golang-jwt/jwt/v5"
	"github.com/labstack/echo/v4"
)

// GetUserIDFromToken extracts the user ID from the JWT token in the context
func GetUserIDFromToken(c echo.Context) (uint, error) {
	userToken, ok := c.Get("user").(*jwt.Token)
	if !ok {
		return 0, fmt.Errorf("token JWT tidak valid atau tidak ditemukan")
	}

	claims, ok := userToken.Claims.(*jwt.MapClaims)
	if !ok {
		return 0, fmt.Errorf("format klaim JWT tidak valid")
	}

	userIDFloat, ok := (*claims)["user_id"].(float64)
	if !ok {
		return 0, fmt.Errorf("ID pengguna dalam token tidak valid")
	}

	userID := uint(userIDFloat)
	return userID, nil
}

// GetUserIDStringFromToken extracts the user ID as string from the JWT token in the context
func GetUserIDStringFromToken(c echo.Context) (string, error) {
	userID, err := GetUserIDFromToken(c)
	if err != nil {
		return "", err
	}

	userIDStr := strconv.FormatUint(uint64(userID), 10)
	return userIDStr, nil
}