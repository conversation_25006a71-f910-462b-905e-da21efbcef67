package tasks

import (
	"log"
	"vpn-shop/backend-go/models/user"

	"gorm.io/gorm"
)

// RunDailyMaintenance menjalankan semua tugas pemeliharaan harian.
// Saat ini, tugas ini mencakup:
// 1. Mereset kuota trial pengguna.
// 2. Mereset penghitung hapus per jam pengguna.
func RunDailyMaintenance(db *gorm.DB, logger *log.Logger) {
	logger.Println("Memulai tugas pemeliharaan harian...")

	// --- Tugas 1: Reset Kuota Trial ---
	logger.Println("Menjalankan sub-tugas: ResetTrialUsers")
	resultTrial := db.Model(&user.User{}).Where("trial > ?", 0).Update("trial", 0)
	if resultTrial.Error != nil {
		logger.Printf("Error saat mereset kuota trial: %v\n", resultTrial.Error)
	} else if resultTrial.RowsAffected > 0 {
		logger.Printf("Berhasil mereset kuota trial untuk %d pengguna.\n", resultTrial.RowsAffected)
	} else {
		logger.Println("Tidak ada kuota trial pengguna yang perlu direset.")
	}

	// --- Tugas 2: Reset Penghitung Hapus Per Jam ---
	logger.Println("Menjalankan sub-tugas: ResetDeleteHourlyCount")
	resultDeleteCount := db.Model(&user.User{}).Where("delete_hourly_count > ?", 0).Update("delete_hourly_count", 0)
	if resultDeleteCount.Error != nil {
		logger.Printf("Error saat mereset delete_hourly_count: %v\n", resultDeleteCount.Error)
	} else if resultDeleteCount.RowsAffected > 0 {
		logger.Printf("Berhasil mereset delete_hourly_count untuk %d pengguna.\n", resultDeleteCount.RowsAffected)
	} else {
		logger.Println("Tidak ada delete_hourly_count yang perlu direset.")
	}

	logger.Println("Tugas pemeliharaan harian selesai.")
}
