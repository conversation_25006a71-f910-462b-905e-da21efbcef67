package tasks

import (
	"log"
	"time"
	"vpn-shop/backend-go/models/account"

	"gorm.io/gorm"
)

// CheckAndExpireAccounts menemukan akun yang telah melewati tanggal kedaluwarsa
// dan memperbarui statusnya menjadi "expired".
func CheckAndExpireAccounts(db *gorm.DB, logger *log.Logger) {
	now := time.Now()
	logger.Println("Menjalankan tugas: CheckAndExpireAccounts")

	// --- Handle AccountTrojan ---
	var trojanAccounts []account.AccountTrojan
	if err := db.Where("status = ? AND expired < ?", "active", now).Find(&trojanAccounts).Error; err != nil {
		logger.Printf("Error mencari akun Trojan yang expired: %v", err)
	} else if len(trojanAccounts) > 0 {
		logger.Printf("Menemukan %d akun Trojan untuk di-expired:", len(trojanAccounts))
		var ids []uint
		for _, acc := range trojanAccounts {
			ids = append(ids, acc.AccountID)
			logger.Printf("  - Username: %s, Tipe: %s, Domain: %s", acc.Username, acc.SubscriptionType, acc.Domain)
		}
		if err := db.Model(&account.AccountTrojan{}).Where("id IN ?", ids).Update("status", "expired").Error; err != nil {
			logger.Printf("Error saat memperbarui akun Trojan: %v", err)
		} else {
			logger.Printf("Berhasil memperbarui %d akun Trojan.", len(trojanAccounts))
		}
	}

	// --- Handle AccountVmess ---
	var vmessAccounts []account.AccountVmess
	if err := db.Where("status = ? AND expired < ?", "active", now).Find(&vmessAccounts).Error; err != nil {
		logger.Printf("Error mencari akun Vmess yang expired: %v", err)
	} else if len(vmessAccounts) > 0 {
		logger.Printf("Menemukan %d akun Vmess untuk di-expired:", len(vmessAccounts))
		var ids []uint
		for _, acc := range vmessAccounts {
			ids = append(ids, acc.AccountID)
			logger.Printf("  - Username: %s, Tipe: %s, Domain: %s", acc.Username, acc.SubscriptionType, acc.Domain)
		}
		if err := db.Model(&account.AccountVmess{}).Where("id IN ?", ids).Update("status", "expired").Error; err != nil {
			logger.Printf("Error saat memperbarui akun Vmess: %v", err)
		} else {
			logger.Printf("Berhasil memperbarui %d akun Vmess.", len(vmessAccounts))
		}
	}

	// --- Handle AccountVless ---
	var vlessAccounts []account.AccountVless
	if err := db.Where("status = ? AND expired < ?", "active", now).Find(&vlessAccounts).Error; err != nil {
		logger.Printf("Error mencari akun Vless yang expired: %v", err)
	} else if len(vlessAccounts) > 0 {
		logger.Printf("Menemukan %d akun Vless untuk di-expired:", len(vlessAccounts))
		var ids []uint
		for _, acc := range vlessAccounts {
			ids = append(ids, acc.AccountID)
			logger.Printf("  - Username: %s, Tipe: %s, Domain: %s", acc.Username, acc.SubscriptionType, acc.Domain)
		}
		if err := db.Model(&account.AccountVless{}).Where("id IN ?", ids).Update("status", "expired").Error; err != nil {
			logger.Printf("Error saat memperbarui akun Vless: %v", err)
		} else {
			logger.Printf("Berhasil memperbarui %d akun Vless.", len(vlessAccounts))
		}
	}

	// --- Handle AccountSsh ---
	var sshAccounts []account.AccountSsh
	if err := db.Where("status = ? AND expired < ?", "active", now).Find(&sshAccounts).Error; err != nil {
		logger.Printf("Error mencari akun Ssh yang expired: %v", err)
	} else if len(sshAccounts) > 0 {
		logger.Printf("Menemukan %d akun Ssh untuk di-expired:", len(sshAccounts))
		var ids []uint
		for _, acc := range sshAccounts {
			ids = append(ids, acc.AccountID)
			logger.Printf("  - Username: %s, Tipe: %s, Domain: %s", acc.Username, acc.SubscriptionType, acc.Domain)
		}
		if err := db.Model(&account.AccountSsh{}).Where("id IN ?", ids).Update("status", "expired").Error; err != nil {
			logger.Printf("Error saat memperbarui akun Ssh: %v", err)
		} else {
			logger.Printf("Berhasil memperbarui %d akun Ssh.", len(sshAccounts))
		}
	}

	logger.Println("Tugas selesai: CheckAndExpireAccounts")
}
