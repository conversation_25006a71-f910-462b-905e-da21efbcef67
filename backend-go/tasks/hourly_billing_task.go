package tasks

import (
	"encoding/csv"
	"errors"
	"fmt"
	"log"
	"math"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"sync"
	"time"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/models/admin"
	"vpn-shop/backend-go/models/payment"
	"vpn-shop/backend-go/models/server"
	"vpn-shop/backend-go/models/user"
	"vpn-shop/backend-go/utils"

	"github.com/go-co-op/gocron/v2"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

var ErrInsufficientBalance = errors.New("insufficient balance")

var hourlyBillingLogger *log.Logger

// SetHourlyBillingLogger sets the logger for the hourly billing cron job.
func SetHourlyBillingLogger(logger *log.Logger) {
	hourlyBillingLogger = logger
}

// init ensures a default logger is available if not set from main.
func init() {
	// This provides a fallback to standard output if the custom logger is not set.
	hourlyBillingLogger = log.New(os.Stdout, "HOURLY_BILLING_TASK: ", log.LstdFlags)
}

// accountTypeToTableName maps an account type string to its database table name.
var accountTypeToTableName = map[string]string{
	"trojan": "account_trojans",
	"vmess":  "account_vmess",
	"vless":  "account_vless",
	"ssh":    "account_ssh",
}

type GenericHourlyAccount struct {
	ID              uint
	UserID          string
	Username        string
	Status          string
	IsHourly        bool
	LastBilledAt    *time.Time
	LastStart       *time.Time
	UnbilledMinutes int
	TotalHours      int
	Harga           string
	AccountType     string // 'trojan', 'vmess', 'vless', 'ssh'
	KodeServer      string
	CreatedAt       time.Time
}

// getTableName maps an account type string to its corresponding table name.
func getTableName(accountType string) (string, error) {
	tableName, ok := accountTypeToTableName[accountType]
	if !ok {
		return "", fmt.Errorf("unknown account type: %s", accountType)
	}
	return tableName, nil
}

// fetchAllGenericHourlyAccounts fetches all active hourly accounts from the database.
func fetchAllGenericHourlyAccounts(tx *gorm.DB) ([]GenericHourlyAccount, error) {
	hourlyBillingLogger.Println("DEBUG: Starting fetch for all hourly accounts...")
	var allAccounts []GenericHourlyAccount

	for accType, tableName := range accountTypeToTableName {
		var accounts []GenericHourlyAccount
		// Note: We must explicitly add 'account_type' as it doesn't exist in the tables themselves.
		selectFields := "*, '" + accType + "' as account_type"
		if err := tx.Table(tableName).Select(selectFields).Where("is_hourly = ? AND status = ? AND deleted_at IS NULL", true, "active").Scan(&accounts).Error; err != nil {
			hourlyBillingLogger.Printf("ERROR: Failed to fetch from %s: %v", tableName, err)
			continue // Continue to next table even if one fails
		}
		if len(accounts) > 0 {
			userIDs := []string{}
			for _, acc := range accounts {
				userIDs = append(userIDs, acc.UserID)
			}

			userMap := make(map[string]string)
			var users []user.User
			// Fetch all user details in one query to be efficient (avoids N+1 problem)
			if err := tx.Model(&user.User{}).Where("id IN ?", userIDs).Find(&users).Error; err == nil {
				for _, u := range users {
					userMap[strconv.FormatUint(uint64(u.ID), 10)] = u.Name
				}
			}

			hourlyBillingLogger.Printf("DEBUG: Found %d active hourly account(s) in table '%s':", len(accounts), tableName)
			for _, acc := range accounts {
				buyerName := userMap[acc.UserID]
				if buyerName == "" {
					buyerName = "<not found>"
				}
				hourlyBillingLogger.Printf("  - Username: %s, Pembeli: %s (ID: %s)", acc.Username, buyerName, acc.UserID)
			}
		}
		allAccounts = append(allAccounts, accounts...)
	}

	hourlyBillingLogger.Printf("DEBUG: Finished fetch. Total active hourly accounts found across all tables: %d", len(allAccounts))
	return allAccounts, nil
}

func processHourlyBilling() {
	hourlyBillingLogger.Println("Starting user-centric hourly billing check...")

	// 1. Fetch all active hourly accounts from all tables
	allAccounts, err := fetchAllGenericHourlyAccounts(db.DB)
	if err != nil {
		hourlyBillingLogger.Printf("ERROR: Failed to fetch hourly accounts: %v", err)
		return
	}

	if len(allAccounts) == 0 {
		hourlyBillingLogger.Println("No active hourly accounts to process.")
		return
	}

	// 2. Group accounts by UserID
	accountsByUser := make(map[string][]GenericHourlyAccount)
	for _, acc := range allAccounts {
		accountsByUser[acc.UserID] = append(accountsByUser[acc.UserID], acc)
	}

	hourlyBillingLogger.Printf("Found %d users with active hourly accounts.", len(accountsByUser))

	// 3. Process billing for each user concurrently
	var wg sync.WaitGroup
	for userID, userAccounts := range accountsByUser {
		wg.Add(1)
		go func(uid string, accs []GenericHourlyAccount) {
			defer wg.Done()
			if err := processUserBilling(uid, accs); err != nil {
				hourlyBillingLogger.Printf("ERROR processing billing for user %s: %v", uid, err)
			}
		}(userID, userAccounts)
	}

	wg.Wait()
	hourlyBillingLogger.Println("User-centric hourly billing check finished.")
}

// processUserBilling handles all billing logic for a single user and their accounts.
func processUserBilling(userID string, accounts []GenericHourlyAccount) error {
	tx := db.DB.Begin()
	if tx.Error != nil {
		return fmt.Errorf("failed to start transaction: %w", tx.Error)
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r) // Re-panic after rollback
		}
	}()

	// Lock the user row for the entire duration of their billing process.
	var userModel user.User
	if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&userModel, "id = ?", userID).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("failed to fetch and lock user %s: %w", userID, err)
	}

	now := time.Now()
	totalHourlyRateForUser := 0
	changesMade := false // Flag to track if any DB write occurred

	// First, calculate the total hourly rate for all of the user's active hourly accounts.
	for _, acc := range accounts {
		pricePerHour, err := strconv.Atoi(acc.Harga)
		if err != nil {
			hourlyBillingLogger.Printf("ERROR: Invalid price format for account %s (%s): %v. Skipping from rate calculation.", acc.Username, acc.AccountType, err)
			continue
		}
		totalHourlyRateForUser += pricePerHour
	}

	currentBalanceInTx := userModel.Saldo

	// Process each account individually within the single transaction.
	for _, acc := range accounts {
		tableName, err := getTableName(acc.AccountType)
		if err != nil {
			hourlyBillingLogger.Printf("ERROR: Could not get table for account %s: %v. Skipping.", acc.Username, err)
			continue
		}

		// --- NEW BILLING LOGIC ---
		if acc.LastStart == nil {
			hourlyBillingLogger.Printf("INFO: Account %s for user %s is active but LastStart is not set. Setting it to now and skipping billing for this cycle.", acc.Username, userModel.Name)
			err = tx.Table(tableName).Where("id = ?", acc.ID).Update("last_start", now).Error
			if err != nil {
				hourlyBillingLogger.Printf("ERROR: Failed to set initial last_start for account %s: %v", acc.Username, err)
			} else {
				changesMade = true
			}
			continue
		}

		durationSinceLastStart := now.Sub(*acc.LastStart)
		minutesSinceLastStart := int(math.Round(durationSinceLastStart.Minutes()))

		if minutesSinceLastStart < 0 {
			hourlyBillingLogger.Printf("WARNING: Negative duration detected for account %s. Clock skew? Ignoring.", acc.Username)
			minutesSinceLastStart = 0
		}

		totalUnbilledMinutes := acc.UnbilledMinutes + minutesSinceLastStart
		hoursToBill := totalUnbilledMinutes / 60
		remainingUnbilledMinutes := totalUnbilledMinutes % 60

		// If not enough minutes have accumulated to bill for a full hour, skip to the next account.
		if hoursToBill < 1 {
			continue
		}

		// --- BILLING EXECUTION ---
		hourlyBillingLogger.Printf("INFO: Account %s for user %s has accumulated %d total minutes. Attempting to bill for %d hour(s).", acc.Username, userModel.Name, totalUnbilledMinutes, hoursToBill)

		pricePerHour, err := strconv.Atoi(acc.Harga)
		if err != nil {
			hourlyBillingLogger.Printf("ERROR: Invalid price format for account %d (%s): %v. Skipping billing.", acc.ID, acc.Username, err)
			continue
		}

		costForThisAccount := int64(hoursToBill * pricePerHour)
		initialBalanceForThisTx := currentBalanceInTx

		// PRE-BILLING CHECK: Can the user pay for this specific account's usage?
		if initialBalanceForThisTx < costForThisAccount {
			hourlyBillingLogger.Printf("WARNING: User %s has insufficient balance (%d) to pay for usage on account %s (cost: %d). Disabling all hourly accounts.", userModel.Name, initialBalanceForThisTx, acc.Username, costForThisAccount)
			for _, a := range accounts {
				if err := disableAccountOnMarzbanAndDB(tx, a.ID, a.AccountType, a.Username, a.KodeServer); err != nil {
					hourlyBillingLogger.Printf("ERROR: Failed during forced account suspension for account %s: %v", a.Username, err)
				}
			}
			// Commit the suspension changes and stop processing this user.
			return tx.Commit().Error
		}

		// DEDUCT BALANCE
		remainingBalanceForThisTx := initialBalanceForThisTx - costForThisAccount
		if err := tx.Model(&user.User{}).Where("id = ?", userID).Update("saldo", remainingBalanceForThisTx).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to deduct balance for user %s for account %s: %w", userModel.Name, acc.Username, err)
		}
		currentBalanceInTx = remainingBalanceForThisTx
		changesMade = true

		hourlyBillingLogger.Printf("Successfully billed user %s for account %s/%s. Cost: %d. Balance before: %d, after: %d.", userModel.Name, acc.AccountType, acc.Username, costForThisAccount, initialBalanceForThisTx, remainingBalanceForThisTx)

		// UPDATE ACCOUNT'S BILLING-RELATED FIELDS
		updateData := map[string]interface{}{
			"last_billed_at":   now,
			"total_hours":      gorm.Expr("total_hours + ?", hoursToBill),
			"unbilled_minutes": remainingUnbilledMinutes,
			"last_start":       now, // Reset the stopwatch
		}
		if err := tx.Table(tableName).Where("id = ?", acc.ID).Updates(updateData).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to update account %d billing data: %w", acc.ID, err)
		}

		// CREATE BILLING HISTORY RECORD
		history := payment.BillingHourlyHistory{
			AccountID:        acc.ID,
			AccountType:      acc.AccountType,
			UserID:           userID,
			AmountBilled:     costForThisAccount,
			InitialBalance:   initialBalanceForThisTx,
			RemainingBalance: remainingBalanceForThisTx,
			BilledAt:         now,
			HoursBilled:      hoursToBill,
		}
		if err := tx.Create(&history).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create billing history for account %d: %w", acc.ID, err)
		}

		// CREATE TRANSACTION RECORD for the billing
		paymentGateway := "SALDO"
		accountID := acc.ID
		accountType := acc.AccountType

		billingTransaction := payment.Transaction{
			InvoiceID:      fmt.Sprintf("INV-H-%d-%s-%d-%s", userModel.ID, acc.AccountType, acc.ID, now.Format("**************")),
			UserID:         userModel.ID,
			AccountID:      &accountID,
			AccountType:    &accountType,
			Type:           payment.BilledHourly,
			Description:    fmt.Sprintf("Penagihan per jam untuk akun %s (%s) - %d jam", acc.Username, acc.AccountType, hoursToBill),
			Amount:         costForThisAccount,
			Status:         payment.Success,
			PaymentGateway: &paymentGateway,
		}
		if err := tx.Create(&billingTransaction).Error; err != nil {
			tx.Rollback()
			return fmt.Errorf("failed to create billing transaction record for account %d: %w", acc.ID, err)
		}

		// Pass copies to the goroutine to avoid capturing loop variables.
		finalAccount := acc
		finalHistory := history
		go func() {
			LogBillingToCSV(userModel, finalAccount, finalHistory)
		}()

		// POST-BILLING CHECK
		if remainingBalanceForThisTx < int64(totalHourlyRateForUser) {
			hourlyBillingLogger.Printf("WARNING: User %s has insufficient balance (%d) for the next hour of service (total rate: %d). Disabling all hourly accounts.", userModel.Name, remainingBalanceForThisTx, totalHourlyRateForUser)
			for _, a := range accounts {
				if err := disableAccountOnMarzbanAndDB(tx, a.ID, a.AccountType, a.Username, a.KodeServer); err != nil {
					hourlyBillingLogger.Printf("ERROR: Failed during post-billing account suspension for account %s: %v", a.Username, err)
				}
			}
			return tx.Commit().Error
		}
	} // End of for loop

	if !changesMade {
		hourlyBillingLogger.Printf("No changes or billable hours for user %s at this time. Rolling back to release lock.", userModel.Name)
		tx.Rollback()
		return nil
	}

	return tx.Commit().Error
}

// disableAccountOnMarzbanAndDB handles the complete suspension of an account from the Marzban server and the local database within a transaction.
func disableAccountOnMarzbanAndDB(tx *gorm.DB, accountID uint, accountType, username, serverCode string) error {
	hourlyBillingLogger.Printf("INFO: Insufficient balance. Suspending account %s (ID: %d, Type: %s)", username, accountID, accountType)

	var server server.Server
	if err := tx.Where("kode = ?", serverCode).First(&server).Error; err != nil {
		return fmt.Errorf("could not find server configuration '%s' for account %s: %w", serverCode, username, err)
	}

	decryptedToken, err := utils.Decrypt(server.Token)
	if err != nil {
		return fmt.Errorf("failed to decrypt server token for %s: %w", serverCode, err)
	}

	domain := server.Domain
	if !strings.HasPrefix(domain, "http://") && !strings.HasPrefix(domain, "https://") {
		domain = "https://" + domain
	}
	marzbanAPIURL := fmt.Sprintf("%s/api/user/%s", domain, username)

	payload := strings.NewReader(`{"status": "disabled"}`)

	req, err := http.NewRequest("PUT", marzbanAPIURL, payload)
	if err != nil {
		return fmt.Errorf("failed to create disable request for user %s on server %s: %w", username, serverCode, err)
	}
	req.Header.Add("Authorization", "Bearer "+decryptedToken)
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("accept", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send disable request to Marzban for user %s: %w", username, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("marzban API returned non-OK status (%d) for disabling user %s", resp.StatusCode, username)
	}

	hourlyBillingLogger.Printf("Successfully disabled user %s on Marzban server %s.", username, serverCode)

	tableName, ok := accountTypeToTableName[accountType]
	if !ok {
		return fmt.Errorf("invalid account type: %s", accountType)
	}

	updateResult := tx.Table(tableName).Where("id = ?", accountID).Update("status", "suspended")
	if updateResult.Error != nil {
		return fmt.Errorf("failed to update account status to suspended for account %d in table %s: %w", accountID, tableName, updateResult.Error)
	}

	if updateResult.RowsAffected == 0 {
		hourlyBillingLogger.Printf("WARNING: No rows were updated for account ID %d in table %s when trying to set status to suspended.", accountID, tableName)
	} else {
		hourlyBillingLogger.Printf("Successfully updated account status to 'suspended' for account ID %d in table %s.", accountID, tableName)
	}

	return nil
}

// LogBillingToCSV writes a billing record to a user-specific CSV file.
func LogBillingToCSV(userModel user.User, account GenericHourlyAccount, history payment.BillingHourlyHistory) {
	csvDir := os.Getenv("CRON_LOG_HOURLY_BILLING_CSV_DIR")
	if csvDir == "" {
		return
	}

	if userModel.Username == "" {
		hourlyBillingLogger.Printf("ERROR: Cannot log to CSV, user with ID %d has no username.", userModel.ID)
		return
	}
	fileName := fmt.Sprintf("%s.csv", userModel.Username)
	filePath := filepath.Join(csvDir, fileName)

	if err := os.MkdirAll(csvDir, os.ModePerm); err != nil {
		hourlyBillingLogger.Printf("ERROR: Failed to create CSV directory %s: %v", csvDir, err)
		return
	}

	var fileExists bool
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		fileExists = false
	} else {
		fileExists = true
	}

	file, err := os.OpenFile(filePath, os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
	if err != nil {
		hourlyBillingLogger.Printf("ERROR: Failed to open or create CSV file %s: %v", filePath, err)
		return
	}
	defer file.Close()

	writer := csv.NewWriter(file)
	defer writer.Flush()

	if !fileExists {
		if err := writer.Write([]string{"BilledAt", "UserID", "Username", "AccountType", "HoursBilled", "AmountBilled", "InitialBalance", "RemainingBalance"}); err != nil {
			hourlyBillingLogger.Printf("ERROR: Failed to write CSV header to %s: %v", filePath, err)
			return
		}
	}

	record := []string{
		history.BilledAt.Format(time.RFC3339),
		history.UserID,
		userModel.Username,
		history.AccountType,
		strconv.Itoa(history.HoursBilled),
		strconv.FormatInt(history.AmountBilled, 10),
		strconv.FormatInt(history.InitialBalance, 10),
		strconv.FormatInt(history.RemainingBalance, 10),
	}

	if err := writer.Write(record); err != nil {
		hourlyBillingLogger.Printf("ERROR: Failed to write record to CSV %s: %v", filePath, err)
	}
}

// ScheduleHourlyBilling adds the hourly billing job to an existing scheduler.
func ScheduleHourlyBilling(s gocron.Scheduler) error {
	settings, err := utils.GetSettings()
	if err != nil {
		hourlyBillingLogger.Printf("ERROR: Could not retrieve admin settings: %v. Using default billing interval of 1 minute.", err)
		// Fallback to a default setting if fetching fails
		settings = &admin.AdminSetting{HourlyBillingInterval: 1}
	}

	interval := settings.HourlyBillingInterval
	if interval < 1 {
		interval = 1 // Ensure interval is at least 1 minute to avoid issues.
	}

	hourlyBillingLogger.Printf("Scheduling hourly billing job to run every %d minute(s).", interval)

	_, err = s.NewJob(
		gocron.DurationJob(time.Duration(interval)*time.Minute),
		gocron.NewTask(processHourlyBilling),
	)
	if err != nil {
		return fmt.Errorf("failed to schedule hourly billing job: %w", err)
	}

	hourlyBillingLogger.Println("Hourly billing job scheduled successfully.")
	return nil
}
