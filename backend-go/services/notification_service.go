package services

import (
	"encoding/json"
	"fmt"
	"time"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/models/notification"

	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// NotificationService handles notification operations
type NotificationService struct {
	db *gorm.DB
}

// NewNotificationService creates a new notification service
func NewNotificationService() *NotificationService {
	return &NotificationService{
		db: db.DB,
	}
}

// CreateNotification creates a new notification for a user
func (ns *NotificationService) CreateNotification(userID uint, notifType notification.NotificationType, title, message string, data *notification.NotificationData) (*notification.Notification, error) {
	notif := &notification.Notification{
		UserID:  userID,
		Type:    notifType,
		Title:   title,
		Message: message,
		IsRead:  false,
	}

	// Convert data to JSON if provided
	if data != nil {
		dataJSON, err := json.Marshal(data)
		if err != nil {
			log.WithError(err).Error("Failed to marshal notification data")
		} else {
			dataStr := string(dataJSON)
			notif.Data = &dataStr
		}
	}

	if err := ns.db.Create(notif).Error; err != nil {
		log.WithError(err).Error("Failed to create notification")
		return nil, err
	}

	log.WithFields(log.Fields{
		"user_id": userID,
		"type":    notifType,
		"title":   title,
	}).Info("Notification created successfully")

	return notif, nil
}

// GetUserNotifications retrieves notifications for a user with pagination
func (ns *NotificationService) GetUserNotifications(userID uint, page, limit int) (*notification.NotificationListResponse, error) {
	var notifications []notification.Notification
	var total int64
	var unreadCount int64

	offset := (page - 1) * limit

	// Get total count
	if err := ns.db.Model(&notification.Notification{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, err
	}

	// Get unread count
	if err := ns.db.Model(&notification.Notification{}).Where("user_id = ? AND is_read = ?", userID, false).Count(&unreadCount).Error; err != nil {
		return nil, err
	}

	// Get notifications with pagination
	if err := ns.db.Where("user_id = ?", userID).
		Order("created_at DESC").
		Offset(offset).
		Limit(limit).
		Find(&notifications).Error; err != nil {
		return nil, err
	}

	// Convert to response format
	var notificationResponses []notification.NotificationResponse
	for _, notif := range notifications {
		notificationResponses = append(notificationResponses, notif.ToResponse())
	}

	return &notification.NotificationListResponse{
		Notifications: notificationResponses,
		Total:         total,
		Page:          page,
		Limit:         limit,
		UnreadCount:   unreadCount,
	}, nil
}

// MarkAsRead marks notifications as read
func (ns *NotificationService) MarkAsRead(userID uint, notificationIDs []uint) error {
	now := time.Now()
	
	result := ns.db.Model(&notification.Notification{}).
		Where("user_id = ? AND id IN ?", userID, notificationIDs).
		Updates(map[string]interface{}{
			"is_read": true,
			"read_at": now,
		})

	if result.Error != nil {
		log.WithError(result.Error).Error("Failed to mark notifications as read")
		return result.Error
	}

	log.WithFields(log.Fields{
		"user_id":          userID,
		"notification_ids": notificationIDs,
		"affected_rows":    result.RowsAffected,
	}).Info("Notifications marked as read")

	return nil
}

// MarkAllAsRead marks all notifications as read for a user
func (ns *NotificationService) MarkAllAsRead(userID uint) error {
	now := time.Now()
	
	result := ns.db.Model(&notification.Notification{}).
		Where("user_id = ? AND is_read = ?", userID, false).
		Updates(map[string]interface{}{
			"is_read": true,
			"read_at": now,
		})

	if result.Error != nil {
		log.WithError(result.Error).Error("Failed to mark all notifications as read")
		return result.Error
	}

	log.WithFields(log.Fields{
		"user_id":       userID,
		"affected_rows": result.RowsAffected,
	}).Info("All notifications marked as read")

	return nil
}

// GetNotificationStats gets notification statistics for a user
func (ns *NotificationService) GetNotificationStats(userID uint) (*notification.NotificationStatsResponse, error) {
	var total, unread int64

	// Get total count
	if err := ns.db.Model(&notification.Notification{}).Where("user_id = ?", userID).Count(&total).Error; err != nil {
		return nil, err
	}

	// Get unread count
	if err := ns.db.Model(&notification.Notification{}).Where("user_id = ? AND is_read = ?", userID, false).Count(&unread).Error; err != nil {
		return nil, err
	}

	return &notification.NotificationStatsResponse{
		UnreadCount: unread,
		TotalCount:  total,
	}, nil
}

// DeleteNotification deletes a notification (soft delete)
func (ns *NotificationService) DeleteNotification(userID, notificationID uint) error {
	result := ns.db.Where("user_id = ? AND id = ?", userID, notificationID).Delete(&notification.Notification{})
	
	if result.Error != nil {
		log.WithError(result.Error).Error("Failed to delete notification")
		return result.Error
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("notification not found or not owned by user")
	}

	log.WithFields(log.Fields{
		"user_id":         userID,
		"notification_id": notificationID,
	}).Info("Notification deleted")

	return nil
}

// Helper functions for creating specific notification types

// CreateTrialNotification creates a notification for trial account creation
func CreateTrialNotification(userID uint, username, serverCode string) error {
	service := NewNotificationService()
	
	data := &notification.NotificationData{
		Username:   &username,
		ServerCode: &serverCode,
	}

	_, err := service.CreateNotification(
		userID,
		notification.NotificationTypeTrial,
		"Akun Trial Berhasil Dibuat",
		fmt.Sprintf("Akun trial %s berhasil dibuat di server %s", username, serverCode),
		data,
	)

	return err
}

// CreateMonthlyPurchaseNotification creates a notification for monthly purchase
func CreateMonthlyPurchaseNotification(userID uint, username, serverCode string, duration int, amount int64) error {
	service := NewNotificationService()
	
	durationStr := fmt.Sprintf("%d bulan", duration)
	data := &notification.NotificationData{
		Username:   &username,
		ServerCode: &serverCode,
		Amount:     &amount,
		Duration:   &durationStr,
	}

	_, err := service.CreateNotification(
		userID,
		notification.NotificationTypeMonthly,
		"Pembelian Akun Bulanan Berhasil",
		fmt.Sprintf("Akun %s berhasil dibeli untuk %d bulan di server %s", username, duration, serverCode),
		data,
	)

	return err
}

// CreateTopupNotification creates a notification for balance top-up
func CreateTopupNotification(userID uint, amount int64, paymentMethod string) error {
	service := NewNotificationService()
	
	data := &notification.NotificationData{
		Amount:        &amount,
		PaymentMethod: &paymentMethod,
	}

	_, err := service.CreateNotification(
		userID,
		notification.NotificationTypeTopup,
		"Top Up Saldo Berhasil",
		fmt.Sprintf("Saldo berhasil ditambah Rp %s melalui %s", formatRupiah(amount), paymentMethod),
		data,
	)

	return err
}

// Helper function to format rupiah (you can move this to utils if needed)
func formatRupiah(amount int64) string {
	return fmt.Sprintf("%d", amount) // Simplified, you can use proper formatting
}
