package services

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"vpn-shop/backend-go/models/server"
	"vpn-shop/backend-go/models/shared"
	"vpn-shop/backend-go/utils"

	"github.com/labstack/echo/v4"
)

// marzbanClient manages requests to a Marzban server.
type marzbanClient struct {
	BaseURL    string
	APIToken   string
	HttpClient *http.Client
}

// newMarzbanClient creates a new client for interacting with the Marzban API.
func newMarzbanClient(apiURL, apiToken string) *marzbanClient {
	if !strings.HasPrefix(apiURL, "http://") && !strings.HasPrefix(apiURL, "https://") {
		apiURL = "https://" + apiURL
	}
	return &marzbanClient{
		BaseURL:    apiURL,
		APIToken:   apiToken,
		HttpClient: &http.Client{Timeout: 15 * time.Second},
	}
}

// doRequest performs a standardized HTTP request to the Marzban API.
func (c *marzbanClient) doRequest(method, endpoint string, payload io.Reader) (*http.Response, error) {
	url := fmt.Sprintf("%s/api%s", c.BaseURL, endpoint)
	req, err := http.NewRequest(method, url, payload)
	if err != nil {
		return nil, fmt.Errorf("gagal membuat request ke %s: %w", endpoint, err)
	}

	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	req.Header.Set("Authorization", "Bearer "+c.APIToken)

	return c.HttpClient.Do(req)
}

// --- Public Functions ---

// CreateUser sends a request to the Marzban API to create a new user.
func CreateUser(apiURL, apiToken string, payload MarzbanUserRequest) error {
	client := newMarzbanClient(apiURL, apiToken)

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("gagal marshal payload: %w", err)
	}

	resp, err := client.doRequest("POST", "/user", bytes.NewBuffer(payloadBytes))
	if err != nil {
		return fmt.Errorf("gagal melakukan request pembuatan user: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("marzban API error (create user %d): %s", resp.StatusCode, string(body))
	}

	return nil
}



// GetUserDetails fetches detailed information for a specific user from the Marzban API.
func GetUserDetails(apiURL, apiToken, username string) (*shared.MarzbanUserResponse, error) {
	client := newMarzbanClient(apiURL, apiToken)
	resp, err := client.doRequest("GET", fmt.Sprintf("/user/%s", username), nil)
	if err != nil {
		return nil, fmt.Errorf("gagal mendapatkan detail user: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("marzban API error (user details %d): %s", resp.StatusCode, string(body))
	}

	type FullUserResponse struct {
		Username        string                 `json:"username"`
		Status          string                 `json:"status"`
		UsedTraffic     int64                  `json:"used_traffic"`
		DataLimit       int64                  `json:"data_limit"`
		Links           []string               `json:"links"`
		SubscriptionURL string                 `json:"subscription_url"`
		Proxies         map[string]interface{} `json:"proxies"`
	}

	var fullResponse FullUserResponse
	if err := json.NewDecoder(resp.Body).Decode(&fullResponse); err != nil {
		return nil, fmt.Errorf("gagal decode response detail user: %w", err)
	}

	var protocol string
	for p := range fullResponse.Proxies {
		protocol = p
		break
	}

	return &shared.MarzbanUserResponse{
		Username:        fullResponse.Username,
		Status:          fullResponse.Status,
		UsedTraffic:     fullResponse.UsedTraffic,
		DataLimit:       fullResponse.DataLimit,
		Links:           fullResponse.Links,
		SubscriptionURL: fullResponse.SubscriptionURL,
		Protocol:        protocol,
	}, nil
}

// GetUser checks if a user exists on the Marzban server.
// It returns the user details if found, or an error if not found or on other failures.
func GetUser(apiURL, apiToken, username string) (*shared.MarzbanUserResponse, error) {
	client := newMarzbanClient(apiURL, apiToken)
	endpoint := fmt.Sprintf("/api/user/%s", username)
	resp, err := client.doRequest("GET", endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("gagal memeriksa pengguna di Marzban: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode == http.StatusNotFound {
		// This is not a technical error, but a valid business case: user does not exist.
		return nil, nil // Return nil, nil to indicate user not found.
	}

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return nil, fmt.Errorf("respons tidak valid dari API Marzban (%d): %s", resp.StatusCode, string(body))
	}

	var userResponse shared.MarzbanUserResponse
	if err := json.NewDecoder(resp.Body).Decode(&userResponse); err != nil {
		return nil, fmt.Errorf("gagal mem-parsing respons pengguna dari Marzban: %w", err)
	}

	return &userResponse, nil
}

// CheckAPIConnection verifies the connection and token validity with the Marzban server.
func CheckAPIConnection(apiURL, apiToken string) error {
	client := newMarzbanClient(apiURL, apiToken)
	resp, err := client.doRequest("GET", "/admin", nil)
	if err != nil {
		return fmt.Errorf("gagal melakukan pengecekan koneksi API: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("invalid API response (%d): %s", resp.StatusCode, string(body))
	}

	return nil
}

// UpdateUserExpiration sends a request to the Marzban API to update a user's expiration date.
func UpdateUserExpiration(logger echo.Logger, server *server.Server, username string, newExpirationTime time.Time) error {
	decryptedToken, err := utils.Decrypt(server.Token)
	if err != nil {
		logger.Errorf("[MarzbanService-Renew] Gagal mendekripsi token untuk server %s: %v", server.Domain, err)
		return fmt.Errorf("gagal mendekripsi token server: %w", err)
	}

	client := newMarzbanClient(server.Domain, decryptedToken)

	payload := map[string]interface{}{
		"expire": newExpirationTime.Unix(),
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("gagal marshal payload perpanjangan: %w", err)
	}

	resp, err := client.doRequest("PUT", fmt.Sprintf("/user/%s", username), bytes.NewBuffer(payloadBytes))
	if err != nil {
		return fmt.Errorf("gagal melakukan request perpanjangan: %w", err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		errorMsg := fmt.Sprintf("marzban API error (renew %d): %s", resp.StatusCode, string(body))
		logger.Errorf("[MarzbanService-Renew] ERROR: %s", errorMsg)
		return fmt.Errorf("%s", errorMsg)
	}

	return nil
}

// GenerateUniqueMarzbanUsername creates a unique username with a given prefix.
func GenerateUniqueMarzbanUsername(apiURL, apiToken, prefix string) (string, error) {
	for i := 0; i < 5; i++ { // Try up to 5 times to find a unique username
		username, err := utils.GenerateRandomString(8)
		if err != nil {
			return "", fmt.Errorf("gagal membuat string acak untuk username: %w", err)
		}
		finalUsername := fmt.Sprintf("%s-%s", prefix, username)

		user, err := GetUser(apiURL, apiToken, finalUsername)
		if err != nil {
			// This is a technical error, not a 'not found' case, so we should fail.
			return "", fmt.Errorf("gagal memverifikasi keunikan username via API: %w", err)
		}
		if user == nil {
			// User does not exist, so this username is unique and available.
			return finalUsername, nil
		}
	}
	return "", fmt.Errorf("gagal membuat username unik setelah beberapa kali percobaan")
}

// CreateTrojanProxySettings generates the proxy settings payload for a Trojan user.
func CreateTrojanProxySettings(password string) map[string]interface{} {
	return map[string]interface{}{
		"password": password,
	}
}

// CreateVmessProxySettings generates the proxy settings payload for a Vmess user.
func CreateVmessProxySettings(uuid string) map[string]interface{} {
	return map[string]interface{}{
		"id": uuid,
	}
}

// CreateVlessProxySettings generates the proxy settings payload for a Vless user.
func CreateVlessProxySettings(uuid string) map[string]interface{} {
	return map[string]interface{}{
		"id":   uuid,
		"flow": "xtls-rprx-vision",
	}
}
