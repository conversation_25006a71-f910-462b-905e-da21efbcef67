package services

import (
	"bytes"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/hex"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"os"
	"strconv"
	"time"
	"vpn-shop/backend-go/models/payment"
	"vpn-shop/backend-go/models/user"
)

// --- Structs for Tripay API Interaction ---

// TripayCallbackInstruction defines a single payment instruction step.
type TripayCallbackInstruction struct {
	Title string   `json:"title"`
	Steps []string `json:"steps"`
}

// TripayCallbackPayload holds the data sent by Tripay via callback.
type TripayCallbackPayload struct {
	Reference         string                      `json:"reference"`
	MerchantRef       string                      `json:"merchant_ref"`
	PaymentMethod     string                      `json:"payment_method"`
	PaymentMethodCode string                      `json:"payment_method_code"`
	TotalAmount       int                         `json:"total_amount"`
	FeeMerchant       int                         `json:"fee_merchant"`
	FeeCustomer       int                         `json:"fee_customer"`
	TotalFee          int                         `json:"total_fee"`
	AmountReceived    int                         `json:"amount_received"`
	IsClosedPayment   int                         `json:"is_closed_payment"`
	Status            string                      `json:"status"`
	PaidAt            int64                       `json:"paid_at"`
	Note              string                      `json:"note"`
	Instructions      []TripayCallbackInstruction `json:"instructions"`
}

// TripayTransactionRequest defines the payload for creating a new transaction with Tripay.
type TripayTransactionRequest struct {
	Method        string      `json:"method"`
	MerchantRef   string      `json:"merchant_ref"`
	Amount        int         `json:"amount"`
	CustomerName  string      `json:"customer_name"`
	CustomerEmail string      `json:"customer_email"`
	CustomerPhone string      `json:"customer_phone"`
	OrderItems    []OrderItem `json:"order_items"`
	CallbackURL   string      `json:"callback_url"`
	ReturnURL     string      `json:"return_url,omitempty"`
	ExpiredTime   int64       `json:"expired_time,omitempty"`
	Signature     string      `json:"signature"`
}

// OrderItem defines a single item in the order.
type OrderItem struct {
	SKU        string `json:"sku"`
	Name       string `json:"name"`
	Price      int    `json:"price"`
	Quantity   int    `json:"quantity"`
	ProductURL string `json:"product_url,omitempty"`
	ImageURL   string `json:"image_url,omitempty"`
}

// TripayTransactionResponse defines the structure of a successful response from Tripay.
type TripayTransactionResponse struct {
	Success bool                   `json:"success"`
	Message string                 `json:"message"`
	Data    *TripayTransactionData `json:"data"` // Pointer to handle null data on failure
}

// TripayTransactionData contains the detailed data of the created transaction.
type TripayTransactionData struct {
	Reference      string `json:"reference"`
	MerchantRef    string `json:"merchant_ref"`
	PaymentMethod  string `json:"payment_method"`
	PaymentName    string `json:"payment_name"`
	CustomerName   string `json:"customer_name"`
	CustomerEmail  string `json:"customer_email"`
	CustomerPhone  string `json:"customer_phone"`
	Amount         int    `json:"amount"`
	FeeMerchant    int    `json:"fee_merchant"`
	FeeCustomer    int    `json:"fee_customer"`
	TotalFee       int    `json:"total_fee"`
	AmountReceived int    `json:"amount_received"`
	PayCode        string `json:"pay_code"`
	PayURL         string `json:"pay_url"`
	CheckoutURL    string `json:"checkout_url"`
	Status         string `json:"status"`
	ExpiredTime    int64  `json:"expired_time"`
}

// CreateTripayCheckoutURL abstracts the logic for creating a Tripay checkout URL.
func CreateTripayCheckoutURL(transaction payment.Transaction, paymentMethod string, user user.User, orderItems []OrderItem) (*TripayTransactionData, error) {
	apiKey := os.Getenv("TRIPAY_API_KEY")
	privateKey := os.Getenv("TRIPAY_PRIVATE_KEY")
	merchantCode := os.Getenv("TRIPAY_MERCHANT_CODE")

	if apiKey == "" || privateKey == "" || merchantCode == "" {
		return nil, errors.New("konfigurasi Tripay tidak lengkap")
	}

	// Prepare order items
	if len(orderItems) == 0 {
		orderItems = []OrderItem{
			{
				SKU:      string(transaction.Type),
				Name:     transaction.Description,
				Price:    int(transaction.Amount),
				Quantity: 1,
			},
		}
	}

	// Generate Signature
	signatureString := merchantCode + transaction.InvoiceID + strconv.FormatInt(transaction.Amount, 10)
	mac := hmac.New(sha256.New, []byte(privateKey))
	mac.Write([]byte(signatureString))
	signature := hex.EncodeToString(mac.Sum(nil))

	customerEmail := ""
	if user.Email != nil {
		customerEmail = *user.Email
	}
	customerPhone := ""
	if user.Whatsapp != nil {
		customerPhone = *user.Whatsapp
	}

	// Build return URL - semua transaksi ke invoice page dulu
	// Invoice page akan handle auto-redirect berdasarkan transaction type
	baseReturnURL := os.Getenv("TRIPAY_RETURN_URL")
	returnURL := fmt.Sprintf("%s?invoice_id=%s", baseReturnURL, transaction.InvoiceID)

	tripayReq := TripayTransactionRequest{
		Method:        paymentMethod,
		MerchantRef:   transaction.InvoiceID,
		Amount:        int(transaction.Amount),
		CustomerName:  user.Name,
		CustomerEmail: customerEmail,
		CustomerPhone: customerPhone,
		OrderItems:    orderItems,
		CallbackURL:   os.Getenv("TRIPAY_CALLBACK_URL"),
		ReturnURL:     returnURL,
		ExpiredTime:   time.Now().Add(24 * time.Hour).Unix(),
		Signature:     signature,
	}

	return SendTripayRequest(tripayReq, apiKey)
}

// SendTripayRequest abstracts the logic for sending a transaction request to Tripay.
func SendTripayRequest(requestBody TripayTransactionRequest, apiKey string) (*TripayTransactionData, error) {
	baseURL := os.Getenv("TRIPAY_BASE_URL")
	if baseURL == "" {
		return nil, errors.New("TRIPAY_BASE_URL is not set")
	}

	payloadBytes, err := json.Marshal(requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal tripay request: %w", err)
	}

	client := &http.Client{}
	req, err := http.NewRequest("POST", baseURL+"/transaction/create", bytes.NewReader(payloadBytes))
	if err != nil {
		return nil, fmt.Errorf("failed to create new http request: %w", err)
	}

	req.Header.Add("Authorization", "Bearer "+apiKey)
	req.Header.Add("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request to tripay: %w", err)
	}
	defer resp.Body.Close()

	var tripayResp TripayTransactionResponse
	if err := json.NewDecoder(resp.Body).Decode(&tripayResp); err != nil {
		return nil, fmt.Errorf("failed to decode tripay response: %w", err)
	}

	if !tripayResp.Success || tripayResp.Data == nil {
		return nil, fmt.Errorf("tripay transaction failed: %s", tripayResp.Message)
	}

	return tripayResp.Data, nil
}
