package services

import (
	"fmt"
	"time"

	"vpn-shop/backend-go/models/server"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// getServerDetails retrieves server connection details from the database.
func getServerDetails(db *gorm.DB, kodeServer string) (*server.Server, error) {
	var server server.Server
	if err := db.Where("kode = ?", kodeServer).First(&server).Error; err != nil {
		return nil, fmt.Errorf("failed to find server with code %s: %w", kodeServer, err)
	}
	return &server, nil
}

// RenewMarzbanUser handles the business logic for renewing a user.
// It fetches server details and then calls the low-level API function.
func RenewMarzbanUser(logger echo.Logger, db *gorm.DB, kodeServer string, username string, newExpirationTime time.Time) error {
	logger.Infof("[RenewService] Attempting to renew user '%s' on server '%s'", username, kodeServer)

	server, err := getServerDetails(db, kodeServer)
	if err != nil {
		logger.Errorf("[RenewService] ERROR: Failed to get server details for '%s': %v", kodeServer, err)
		return err
	}

	logger.Infof("[RenewService] Found server details: Domain=%s", server.Domain)

	// Call the low-level function in marzban_service to perform the API request
	if err := UpdateUserExpiration(logger, server, username, newExpirationTime); err != nil {
		return fmt.Errorf("gagal memperbarui masa aktif di Marzban untuk user %s di server %s: %w", username, kodeServer, err)
	}

	logger.Infof("[RenewService] Successfully renewed user '%s' on Marzban.", username)
	return nil
}
