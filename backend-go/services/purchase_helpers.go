package services

import (
	"fmt"
	"net/http"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/models/account"
	"vpn-shop/backend-go/models/payment"
	"vpn-shop/backend-go/models/server"
	"vpn-shop/backend-go/models/shared"
	"vpn-shop/backend-go/models/user"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// RollbackType mendefinisikan jenis operasi rollback yang akan dilakukan.
type RollbackType string

const (
	RollbackTypeMonthly RollbackType = "monthly"
	RollbackTypeHourly  RollbackType = "hourly"
	RollbackTypeTrial   RollbackType = "trial"
)

// PerformPurchaseRollback menangani logika rollback transaksional terpusat untuk semua jenis pembelian.
func PerformPurchaseRollback(
	c echo.Context,
	rollbackType RollbackType,
	userID uint,
	serverKode string,
	username string,
	accountType string, // trojan, vmess, vless
	harga int64, // <PERSON>ya untuk tipe bulanan
) error {
	c.Logger().Errorf("CRITICAL: DB transaction succeeded but API call failed for user '%s'. Initiating rollback.", username)

	rollbackErr := db.DB.Transaction(func(tx *gorm.DB) error {
		// 1. Kembalikan statistik server (berlaku untuk semua)
		if err := tx.Model(&server.Server{}).Where("kode = ?", serverKode).Updates(map[string]interface{}{
			"slot_terpakai": gorm.Expr("slot_terpakai - 1"),
			"total_user":    gorm.Expr("total_user - 1"),
		}).Error; err != nil {
			return fmt.Errorf("failed to revert server stats: %w", err)
		}

		// 2. Lakukan rollback spesifik berdasarkan jenis pembelian
		var accountID uint
		var found bool

		// Temukan AccountID terlebih dahulu, karena dibutuhkan untuk menghapus transaksi
		switch accountType {
		case "trojan":
			var acc account.AccountTrojan
			if err := tx.Where("username = ?", username).First(&acc).Error; err == nil {
				accountID = acc.AccountID
				found = true
			}
		case "vmess":
			var acc account.AccountVmess
			if err := tx.Where("username = ?", username).First(&acc).Error; err == nil {
				accountID = acc.AccountID
				found = true
			}
		case "vless":
			var acc account.AccountVless
			if err := tx.Where("username = ?", username).First(&acc).Error; err == nil {
				accountID = acc.AccountID
				found = true
			}
		}

		if !found {
			c.Logger().Warnf("Tidak dapat menemukan akun dengan username %s untuk proses rollback", username)
		}

		switch rollbackType {
		case RollbackTypeMonthly:
			// Kembalikan saldo pengguna
			if err := tx.Model(&user.User{}).Where("id = ?", userID).Update("saldo", gorm.Expr("saldo + ?", harga)).Error; err != nil {
				return fmt.Errorf("gagal mengembalikan saldo user: %w", err)
			}
			// Hapus catatan transaksi yang sesuai
			if found {
				if err := tx.Where("account_id = ? AND type = ?", accountID, payment.PurchaseMonthly).Delete(&payment.Transaction{}).Error; err != nil {
					c.Logger().Warnf("Gagal menghapus transaksi bulanan untuk account_id %d: %v", accountID, err)
				}
			}

		case RollbackTypeTrial:
			// Kembalikan kuota trial pengguna
			if err := tx.Model(&user.User{}).Where("id = ?", userID).Update("trial", gorm.Expr("trial - 1")).Error; err != nil {
				return fmt.Errorf("gagal mengembalikan kuota trial user: %w", err)
			}
			// Hapus catatan transaksi yang sesuai
			if found {
				if err := tx.Where("account_id = ? AND type = ?", accountID, payment.Trial).Delete(&payment.Transaction{}).Error; err != nil {
					c.Logger().Warnf("Gagal menghapus transaksi trial untuk account_id %d: %v", accountID, err)
				}
			}

		case RollbackTypeHourly:
			// Hapus catatan transaksi yang sesuai
			if found {
				if err := tx.Where("account_id = ? AND type = ?", accountID, payment.PurchaseHourly).Delete(&payment.Transaction{}).Error; err != nil {
					c.Logger().Warnf("Gagal menghapus transaksi per jam untuk account_id %d: %v", accountID, err)
				}
			}
		}

		// 3. Hapus akun yang dibuat (berlaku untuk semua)
		var accountToDelete interface{}
		switch accountType {
		case "trojan":
			accountToDelete = &account.AccountTrojan{}
		case "vmess":
			accountToDelete = &account.AccountVmess{}
		case "vless":
			accountToDelete = &account.AccountVless{}
		}

		if accountToDelete != nil {
			if err := tx.Where("username = ? AND kode_server = ?", username, serverKode).Delete(accountToDelete).Error; err != nil {
				return fmt.Errorf("failed to delete account record: %w", err)
			}
		}

		return nil
	})

	if rollbackErr != nil {
		c.Logger().Errorf("FATAL: Rollback failed for user '%s'. Manual cleanup required. Rollback Error: %v", username, rollbackErr)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyediakan akun dan upaya pembatalan juga gagal. Silakan hubungi dukungan."})
	}

	return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Gagal menyediakan akun di server eksternal. Pembelian telah dibatalkan."})
}
