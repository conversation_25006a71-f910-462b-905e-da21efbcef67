2025/07/12 20:58:48 Generate swagger docs....
2025/07/12 20:58:48 Generate general API Info, search dir:./
2025/07/12 20:58:48 Generating shared.SuccessResponse
2025/07/12 20:58:48 Generating shared.ErrorResponse
2025/07/12 20:58:48 Generating shared.AccountDetailResponse
2025/07/12 20:58:48 Generating shared.ProcessedLink
2025/07/12 20:58:48 Generating shared.ServerInfoForAccountDetail
2025/07/12 20:58:48 Generating account.RenewRequest
2025/07/12 20:58:48 Generating shared.RenewSuccessResponse
2025/07/12 20:58:48 Generating admin.CreateAnnouncementRequest
2025/07/12 20:58:48 Generating announcement.AnnouncementResponse
2025/07/12 20:58:48 Generating admin.UpdateAnnouncementRequest
2025/07/12 20:58:48 Generating admin.AdminSetting
2025/07/12 20:58:48 Generating admin.SwaggerUserListResponse
2025/07/12 20:58:48 Generating admin.SwaggerUserDetailResponse
2025/07/12 20:58:48 Generating admin.RoleResponse
2025/07/12 20:58:48 Generating admin.UpdateUserRequest
2025/07/12 20:58:48 Generating auth.TelegramAuthRequest
2025/07/12 20:58:48 Generating auth.RegisterRequest
2025/07/12 20:58:48 Generating payment.Transaction
2025/07/12 20:58:48 Generating user.User
2025/07/12 20:58:48 Generating user.Role
2025/07/12 20:58:48 Skipping 'user.User', recursion detected.
2025/07/12 20:58:48 Generating payment.TransactionType
2025/07/12 20:58:48 Generating payment.TransactionStatus
2025/07/12 20:58:48 Generating payment.TopUpRequest
2025/07/12 20:58:48 Generating shared.TopUpResponse
2025/07/12 20:58:48 Generating payment.CreatePaymentRequest
2025/07/12 20:58:48 Generating payment.PurchaseItem
2025/07/12 20:58:48 Generating payment.TripayChannelsResponse
2025/07/12 20:58:48 Generating payment.PaymentChannel
2025/07/12 20:58:48 Generating payment.Fee
2025/07/12 20:58:48 Generating payment.TotalFee
2025/07/12 20:58:48 Generating payment.TransactionResponse
2025/07/12 20:58:48 Generating public.PublicTransactionResponse
2025/07/12 20:58:48 warning: route GET /public/transactions is declared multiple times
2025/07/12 20:58:48 Generating purchase.PurchaseHourlyRequest
2025/07/12 20:58:48 Generating purchase.PurchaseMonthlyRequest
2025/07/12 20:58:48 Generating purchase.PurchaseTrialRequest
2025/07/12 20:58:48 Generating server.CreateServerRequest
2025/07/12 20:58:48 Generating server.SwaggerServerResponse
2025/07/12 20:58:48 Generating server.PublicServerListResponse
2025/07/12 20:58:48 Generating server.PublicServerResponse
2025/07/12 20:58:48 Generating server.UpdateServerRequest
2025/07/12 20:58:48 Generating server.TestTokenRequest
2025/07/12 20:58:48 Generating stats.TopServerResponse
2025/07/12 20:58:48 Generating vpn-shop_backend-go_handlers_user.UserResponse
2025/07/12 20:58:48 Generating user.SwaggerRoleResponse
2025/07/12 20:58:48 Generating user.UserStatsResponse
2025/07/12 20:58:48 Generating user.UpdateProfileRequest
2025/07/12 20:58:48 Generating user.ChangePasswordRequest
2025/07/12 20:58:48 Generating user.PaginatedInvoicesResponse
2025/07/12 20:58:48 Generating user.ServiceHistoryResponse
2025/07/12 20:58:48 Generating user.ServiceHistoryItem
2025/07/12 20:58:48 Generating user.TransactionListResponse
2025/07/12 20:58:48 Generating user.TransactionResponse
2025/07/12 20:58:48 Generating shared.Pagination
2025/07/12 20:58:48 create docs.go at docs/docs.go
2025/07/12 20:58:48 create swagger.json at docs/swagger.json
2025/07/12 20:58:48 create swagger.yaml at docs/swagger.yaml
