# Dokumentasi Proyek Backend VPN Shop (Go)

Dokumen ini memberikan gambaran teknis yang mendalam mengenai arsitektur, struk<PERSON>, dan alur kerja dari layanan backend aplikasi VPN Shop.

---

## 1. Filosofi & Tujuan Proyek

Backend ini dirancang untuk menjadi API server yang *robust*, e<PERSON><PERSON>n, dan mudah di<PERSON>. Tujuannya adalah untuk menyediakan serangkaian endpoint RESTful yang aman dan terstandarisasi untuk digunakan oleh aplikasi klien.

- **Bahasa**: Go
- **Framework**: Echo
- **Prinsip Utama**: Kode yang bersih, struktur yang jelas, dan respons API yang konsisten.

---

## 2. Tech Stack

- **Bahasa**: Go (versi 1.20+)
- **Framework Web**: Echo v4
- **ORM & Database**: GORM dengan PostgreSQL
- **Otentikasi**: JWT (JSON Web Tokens)
- **Validasi**: go-playground/validator
- **Dokumentasi API**: Swaggo (Swagger)

---

## 3. Menjalankan Proyek Secara Lokal

1.  **Prasyarat**: Pastikan Go (v1.20+) dan PostgreSQL sudah terinstal.
2.  **Konfigurasi Environment**:
    - Salin file `.env.example` menjadi `.env`.
    - Sesuaikan isinya dengan konfigurasi database lokal Anda (`DB_USER`, `DB_PASSWORD`, `DB_NAME`) dan `JWT_SECRET`.
3.  **Instalasi Dependensi**:
    ```bash
    go mod tidy
    ```
4.  **Menjalankan Server**:
    ```bash
    go run main.go
    ```
    Server akan berjalan pada port yang didefinisikan di `.env` (default: `8000`).

---

## 4. Struktur Direktori Proyek

Berikut adalah struktur direktori aktual dari `backend-go` beserta penjelasan fungsinya masing-masing:

```
.
├── api/                  # Definisi API (misalnya, file-file OpenAPI/Swagger).
├── core/                 # Komponen inti atau logika bisnis utama aplikasi.
├── db/                   # Inisialisasi dan koneksi database (GORM).
├── docs/                 # File-file dokumentasi yang dihasilkan (cth: Swagger docs).
├── handlers/             # Lapisan HTTP (controller), menangani request & response dari klien.
├── logs/                 # Direktori untuk menyimpan file log aplikasi.
├── middleware/           # Middleware untuk Echo (cth: otentikasi JWT, logging).
├── models/               # Definisi struct data (entitas database) & request/response body.
├── services/             # Lapisan layanan, berisi logika bisnis yang lebih kompleks yang dipanggil oleh handlers.
├── tasks/                # Logika untuk pekerjaan terjadwal (cron jobs), cth: billing per jam.
├── utils/                # Fungsi-fungsi bantuan (helper) yang dapat digunakan kembali.
├── .env                  # File environment variables untuk konfigurasi lokal (JANGAN DI-COMMIT).
├── main.go               # Titik masuk utama aplikasi, inisialisasi server & routing.
├── go.mod                # File definisi modul dan dependensi Go.
└── README.md             # Panduan ringkas untuk backend.
```

---

## 5. Alur Kerja Aplikasi (Request Lifecycle)

1.  **Inisialisasi**: `main.go` dieksekusi, menginisialisasi koneksi database (`db`), konfigurasi, dan server Echo.
2.  **Routing**: `main.go` juga mendefinisikan semua rute API dan mengaitkannya dengan fungsi `handler` yang sesuai dari direktori `handlers/`.
3.  **Request Masuk**: Klien mengirimkan request HTTP ke salah satu endpoint.
4.  **Middleware**: Sebelum mencapai handler, request akan melewati serangkaian middleware yang terdaftar (misalnya, `middleware.AuthMiddleware` untuk memeriksa validitas token JWT pada endpoint yang terproteksi).
5.  **Handler**: Fungsi di dalam `handlers/` menerima request. Tugas utamanya adalah:
    - Mem-parsing dan memvalidasi data input (parameter, body request).
    - Memanggil fungsi dari `services/` yang relevan untuk menjalankan logika bisnis.
    - Mengembalikan respons HTTP dalam format JSON.
6.  **Service**: Fungsi di dalam `services/` berisi logika bisnis inti. Fungsi ini berinteraksi dengan database melalui GORM, menggunakan `models/` sebagai representasi data.
7.  **Response**: Handler mengirimkan kembali respons JSON ke klien.
    - **Sukses**: Berisi data yang diminta atau pesan konfirmasi (`map[string]string`).
    - **Error**: Selalu menggunakan struktur standar `shared.ErrorResponse` (`{"error": "pesan..."}`).

---

## 6. Standar Respons API

- **Respons Sukses (Data)**: Mengembalikan objek atau array objek JSON sesuai permintaan.
- **Respons Sukses (Aksi)**: Mengembalikan `map[string]string` dengan kunci `"message"`. Contoh: `{"message": "Pengguna berhasil dihapus"}`.
- **Respons Error**: Selalu mengembalikan objek `shared.ErrorResponse` dengan status HTTP yang sesuai. Contoh: `{"error": "Data tidak ditemukan"}`.
