# VPN Shop - Backend (Go)

Ini adalah layanan backend untuk aplikasi VPN Shop, dibangun menggunakan bahasa Go dengan framework Echo. Backend ini bertanggung jawab untuk mengelola data pengguna, server, otentikasi, dan logika bisnis lainnya.

## Tech Stack

- **Bahasa**: [Go](https://golang.org/) (versi 1.20+)
- **Framework Web**: [Echo](https://echo.labstack.com/) v4
- **ORM**: [GORM](https://gorm.io/)
- **Database**: PostgreSQL
- **Validasi**: [Validator](https://github.com/go-playground/validator)
- **Otentikasi**: JWT (JSON Web Tokens)
- **Dokumentasi API**: [Swagger](https://swagger.io/)

---

## Panduan Integrasi untuk Frontend

Bagian ini menjelaskan poin-poin penting yang perlu diketahui oleh tim frontend untuk berinteraksi dengan API backend.

### 1. Menjalankan Proyek Backend

Pastikan Anda memiliki Go dan PostgreSQL terinstal.

1.  **Clone repository**.
2.  **Setup Environment Variables**: Buat file `.env` di root proyek dan isi variabel yang dibutuhkan (lihat `main.go` untuk daftar variabel yang digunakan, seperti `DB_USER`, `DB_PASSWORD`, `DB_NAME`, `JWT_SECRET`).
3.  **Install Dependencies**:
    ```bash
    go mod tidy
    ```
4.  **Jalankan Server**:
    ```bash
    go run main.go
    ```
    Server akan berjalan di port yang ditentukan (default: `8000`).

### 2. Alur Otentikasi (JWT)

- **Login**: Kirim `email` dan `password` ke endpoint `POST /auth/login`.
- **Respons Login Sukses**: Jika berhasil, API akan mengembalikan token JWT.
    ```json
    {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
    }
    ```
- **Mengakses Endpoint Terproteksi**: Untuk setiap permintaan ke endpoint yang memerlukan otentikasi, sertakan token tersebut di header `Authorization` dengan skema `Bearer`.
    ```
    Authorization: Bearer <token_jwt_anda>
    ```
- **Token Kedaluwarsa/Tidak Valid**: Jika token tidak valid atau sudah kedaluwarsa, API akan mengembalikan status `401 Unauthorized` dengan respons error standar.

### 3. Format Respons API Standar

Untuk memastikan konsistensi, API ini menggunakan format respons yang terstandarisasi.

#### **Respons Sukses**

- **Untuk pengambilan data (GET)**, respons akan berisi data yang diminta dalam format JSON.
- **Untuk aksi (POST, PUT, DELETE)** yang hanya memerlukan konfirmasi, respons akan berisi pesan sukses:
    ```json
    // Contoh: DELETE /admin/users/{id}
    {
        "message": "Pengguna berhasil dihapus"
    }
    ```

#### **Respons Error (PENTING!)**

Semua respons error (status `400`, `401`, `403`, `404`, `500`, dll.) di seluruh API akan mengikuti struktur `shared.ErrorResponse` yang sama. Ini membuat penanganan error di sisi frontend menjadi lebih mudah dan dapat diprediksi.

**Struktur Error Standar:**
```json
{
    "error": "Pesan error yang jelas dalam Bahasa Indonesia."
}
```

**Contoh Kasus:**

- **Input Tidak Valid (400 Bad Request):**
    ```json
    {
        "error": "Email tidak boleh kosong"
    }
    ```
- **Tidak Terotentikasi (401 Unauthorized):**
    ```json
    {
        "error": "Token JWT tidak valid atau tidak ditemukan"
    }
    ```
- **Data Tidak Ditemukan (404 Not Found):**
    ```json
    {
        "error": "Pengguna tidak ditemukan"
    }
    ```

### 4. Dokumentasi API (Swagger)

Dokumentasi lengkap untuk semua endpoint API tersedia melalui Swagger.

- **URL**: `http://localhost:8000/swagger/index.html` (setelah server dijalankan).

Swagger menyediakan detail tentang setiap endpoint, termasuk:
- HTTP Method dan URL.
- Parameter yang dibutuhkan (path, query, body).
- Contoh *request body*.
- Struktur respons untuk sukses dan error.

**Selalu rujuk ke dokumentasi Swagger sebagai sumber kebenaran utama saat melakukan integrasi.**
