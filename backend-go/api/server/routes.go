package server

import (
	"vpn-shop/backend-go/handlers/server"
	"vpn-shop/backend-go/middleware"

	"github.com/labstack/echo/v4"
)

// SetupServerRoutes sets up all routes for VPS server management.
func SetupServerRoutes(e *echo.Group) {
	// Publicly accessible GET routes for all authenticated users
	e.GET("", server.GetAllServers)
	e.GET("/:id", server.GetServerByID)

	// Routes requiring admin privileges
	e.POST("", server.CreateServer, middleware.AdminMiddleware)
	e.PUT("/:id", server.UpdateServer, middleware.AdminMiddleware)
	e.DELETE("/:id", server.DeleteServer, middleware.AdminMiddleware)
	e.POST("/migrate", server.MigrateFromLegacyServers, middleware.AdminMiddleware)

	// Test token endpoints
	e.GET("/:id/test-token", server.TestServerToken)
	e.POST("/test-token", server.TestNewServerToken, middleware.AdminMiddleware)
}
