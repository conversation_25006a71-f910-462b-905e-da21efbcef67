package account

import (
	"vpn-shop/backend-go/handlers/account"
	"vpn-shop/backend-go/handlers/user"

	"github.com/labstack/echo/v4"
)

// SetupAccountRoutes configures the routes for managing existing accounts.
func SetupAccountRoutes(group *echo.Group) {
	group.POST("/renew", account.RenewAccount)
	group.GET("/detail/:username/:server_code", account.GetAccountDetail)
	group.DELETE("/:account_id", account.DeleteAccount)
	group.PUT("/:account_id/activate", account.ActivateAccount)
	group.PUT("/:account_id/disable", account.DisableAccount)
	group.GET("/history", user.GetHistory)
}
