package public

import (
	"vpn-shop/backend-go/handlers/announcement"
	"vpn-shop/backend-go/handlers/public"
	"vpn-shop/backend-go/handlers/stats"

	"github.com/labstack/echo/v4"
)

// SetupPublicRoutes configures the public routes.
func SetupPublicRoutes(e *echo.Group) {
	// Create public group
	publicGroup := e.Group("/public")

	// Transaction routes
	publicGroup.GET("/transactions", public.GetPublicTransactions)

	// Announcement routes
	publicGroup.GET("/announcements", announcement.GetAllAnnouncements)
	publicGroup.GET("/announcements/:id", announcement.GetAnnouncementByID)

	// Stats routes
	publicGroup.GET("/stats/top-servers", stats.GetTopPurchaseServers)
}
