package server

import (
	"time"
	"vpn-shop/backend-go/models/shared"

	"gorm.io/gorm"
)

// Server model for VPS servers
type Server struct {
	ServerID      uint           `gorm:"primarykey;column:id" json:"server_id"`
	CreatedAt     time.Time      `gorm:"type:timestamp without time zone"`
	UpdatedAt     time.Time      `gorm:"type:timestamp without time zone"`
	DeletedAt     gorm.DeletedAt `gorm:"index;type:timestamp with time zone"`
	Nama          string         `gorm:"type:varchar(255);not null" json:"nama"`
	Kode          string         `gorm:"type:varchar(255);uniqueIndex;not null" json:"kode"`
	Domain        string         `gorm:"type:varchar(255);not null" json:"domain"`
	Token         string         `gorm:"type:text;not null" json:"token"`
	Negara        string         `gorm:"type:varchar(255);not null" json:"negara"`
	NamaIsp       string         `gorm:"type:varchar(255);not null" json:"nama_isp"`
	HargaMember   int64          `gorm:"type:numeric(15,0);not null" json:"harga_member"`
	HargaReseller int64          `gorm:"type:numeric(15,0);not null" json:"harga_reseller"`
	SSH           *string        `gorm:"type:varchar(255)" json:"ssh"`
	Trojan        *string        `gorm:"type:varchar(255)" json:"trojan"`
	Vmess         *string        `gorm:"type:varchar(255)" json:"vmess"`
	Vless         *string        `gorm:"type:varchar(255)" json:"vless"`
	SlotServer    int            `gorm:"type:integer;not null" json:"slot_server"`
	SlotTerpakai  *int           `gorm:"type:integer;default:0" json:"slot_terpakai"`
	TotalUser     *int           `gorm:"type:integer;default:0" json:"total_user"`
	MaxDevice     int            `gorm:"type:integer;default:1;not null" json:"max_device"`
}

// CreateServerRequest defines the request body for creating a server.
type CreateServerRequest struct {
	Nama          string  `json:"nama" validate:"required"`
	Kode          string  `json:"kode"` // Made optional - will be auto-generated if empty
	Domain        string  `json:"domain" validate:"required"`
	Token         string  `json:"token" validate:"required"`
	Negara        string  `json:"negara" validate:"required"`
	NamaIsp       string  `json:"nama_isp" validate:"required"`
	HargaMember   int64   `json:"harga_member" validate:"required,min=0"`
	HargaReseller int64   `json:"harga_reseller" validate:"required,min=0"`
	SSH           *string `json:"ssh"`
	Trojan        *string `json:"trojan"`
	Vmess         *string `json:"vmess"`
	Vless         *string `json:"vless"`
	SlotServer    int     `json:"slot_server" validate:"required,min=1"`
	SlotTerpakai  *int    `json:"slot_terpakai"`
	TotalUser     *int    `json:"total_user"`
	MaxDevice     int     `json:"max_device" validate:"required,min=1"`
}

// UpdateServerRequest defines the request body for updating a server.
type UpdateServerRequest struct {
	Nama          *string `json:"nama"`
	Domain        *string `json:"domain"`
	Token         *string `json:"token"`
	Negara        *string `json:"negara"`
	NamaIsp       *string `json:"nama_isp"`
	HargaMember   *int64  `json:"harga_member" validate:"omitempty,min=0"`
	HargaReseller *int64  `json:"harga_reseller" validate:"omitempty,min=0"`
	SSH           *string `json:"ssh"`
	Trojan        *string `json:"trojan"`
	Vmess         *string `json:"vmess"`
	Vless         *string `json:"vless"`
	SlotServer    *int    `json:"slot_server" validate:"omitempty,min=1"`
	SlotTerpakai  *int    `json:"slot_terpakai"`
	TotalUser     *int    `json:"total_user"`
	MaxDevice     *int    `json:"max_device" validate:"omitempty,min=1"`
}

// TestTokenRequest defines the request body for testing a new server token.
type TestTokenRequest struct {
	Domain  string `json:"domain" validate:"required"`
	Token   string `json:"token" validate:"required"`
	ApiPath string `json:"api_path" validate:"required"`
}

// ServerResponse defines the response structure for server data.
type ServerResponse struct {
	ServerID      uint      `json:"server_id"`
	Nama          string    `json:"nama"`
	Kode          string    `json:"kode"`
	Domain        string    `json:"domain"`
	Token         string    `json:"token"`
	Negara        string    `json:"negara"`
	NamaIsp       string    `json:"nama_isp"`
	HargaMember   int64     `json:"harga_member"`
	HargaReseller int64     `json:"harga_reseller"`
	SSH           string    `json:"ssh"`
	Trojan        string    `json:"trojan"`
	Vmess         string    `json:"vmess"`
	Vless         string    `json:"vless"`
	SlotServer    int       `json:"slot_server"`
	SlotTerpakai  int       `json:"slot_terpakai"`
	TotalUser     int       `json:"total_user"`
	MaxDevice     int       `json:"max_device"`
	CreatedAt     time.Time `json:"created_at" gorm:"type:timestamp without time zone"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"type:timestamp without time zone"`
}

// ToResponse converts a Server model to a ServerResponse.
func (s *Server) ToResponse() ServerResponse {
	ssh := ""
	if s.SSH != nil {
		ssh = *s.SSH
	}

	trojan := ""
	if s.Trojan != nil {
		trojan = *s.Trojan
	}

	vmess := ""
	if s.Vmess != nil {
		vmess = *s.Vmess
	}

	vless := ""
	if s.Vless != nil {
		vless = *s.Vless
	}

	slotTerpakai := 0
	if s.SlotTerpakai != nil {
		slotTerpakai = *s.SlotTerpakai
	}

	totalUser := 0
	if s.TotalUser != nil {
		totalUser = *s.TotalUser
	}

	return ServerResponse{
		ServerID:      s.ServerID,
		Nama:          s.Nama,
		Kode:          s.Kode,
		Domain:        s.Domain,
		Token:         s.Token,
		Negara:        s.Negara,
		NamaIsp:       s.NamaIsp,
		HargaMember:   s.HargaMember,
		HargaReseller: s.HargaReseller,
		SSH:           ssh,
		Trojan:        trojan,
		Vmess:         vmess,
		Vless:         vless,
		SlotServer:    s.SlotServer,
		SlotTerpakai:  slotTerpakai,
		TotalUser:     totalUser,
		MaxDevice:     s.MaxDevice,
		CreatedAt:     s.CreatedAt,
		UpdatedAt:     s.UpdatedAt,
	}
}

// ServerListResponse for returning a list of servers.
type ServerListResponse struct {
	Servers []ServerResponse `json:"servers"`
}

// PublicServerResponse defines the response structure for server data exposed to regular users.
// It omits sensitive fields like the token.
type PublicServerResponse struct {
	ServerID      uint      `json:"server_id"`
	Nama          string    `json:"nama"`
	Kode          string    `json:"kode"`
	Domain        string    `json:"domain"`
	Negara        string    `json:"negara"`
	NamaIsp       string    `json:"nama_isp"`
	HargaMember   int64     `json:"harga_member"`
	HargaReseller int64     `json:"harga_reseller"`
	SSH           string    `json:"ssh"`
	Trojan        string    `json:"trojan"`
	Vmess         string    `json:"vmess"`
	Vless         string    `json:"vless"`
	SlotServer    int       `json:"slot_server"`
	SlotTerpakai  int       `json:"slot_terpakai"`
	TotalUser     int       `json:"total_user"`
	MaxDevice     int       `json:"max_device"`
	CreatedAt     time.Time `json:"created_at" gorm:"type:timestamp without time zone"`
	UpdatedAt     time.Time `json:"updated_at" gorm:"type:timestamp without time zone"`
}

// ToServerInfoForAccountDetail converts a Server model to a public-facing ServerInfoForAccountDetail.
func (s *Server) ToServerInfoForAccountDetail() shared.ServerInfoForAccountDetail {
	ssh := ""
	if s.SSH != nil {
		ssh = *s.SSH
	}
	trojan := ""
	if s.Trojan != nil {
		trojan = *s.Trojan
	}
	vmess := ""
	if s.Vmess != nil {
		vmess = *s.Vmess
	}
	vless := ""
	if s.Vless != nil {
		vless = *s.Vless
	}

	return shared.ServerInfoForAccountDetail{
		ServerID:      s.ServerID,
		Nama:          s.Nama,
		Kode:          s.Kode,
		Domain:        s.Domain,
		Negara:        s.Negara,
		NamaIsp:       s.NamaIsp,
		HargaMember:   s.HargaMember,
		HargaReseller: s.HargaReseller,
		SSH:           ssh,
		Trojan:        trojan,
		Vmess:         vmess,
		Vless:         vless,
		CreatedAt:     s.CreatedAt,
		UpdatedAt:     s.UpdatedAt,
	}
}

// ToPublicResponse converts a Server model to a PublicServerResponse, omitting sensitive data.
func (s *Server) ToPublicResponse() PublicServerResponse {
	ssh := ""
	if s.SSH != nil {
		ssh = *s.SSH
	}
	trojan := ""
	if s.Trojan != nil {
		trojan = *s.Trojan
	}
	vmess := ""
	if s.Vmess != nil {
		vmess = *s.Vmess
	}
	vless := ""
	if s.Vless != nil {
		vless = *s.Vless
	}
	slotTerpakai := 0
	if s.SlotTerpakai != nil {
		slotTerpakai = *s.SlotTerpakai
	}
	totalUser := 0
	if s.TotalUser != nil {
		totalUser = *s.TotalUser
	}

	return PublicServerResponse{
		ServerID:      s.ServerID,
		Nama:          s.Nama,
		Kode:          s.Kode,
		Domain:        s.Domain,
		Negara:        s.Negara,
		NamaIsp:       s.NamaIsp,
		HargaMember:   s.HargaMember,
		HargaReseller: s.HargaReseller,
		SSH:           ssh,
		Trojan:        trojan,
		Vmess:         vmess,
		Vless:         vless,
		SlotServer:    s.SlotServer,
		SlotTerpakai:  slotTerpakai,
		TotalUser:     totalUser,
		MaxDevice:     s.MaxDevice,
		CreatedAt:     s.CreatedAt,
		UpdatedAt:     s.UpdatedAt,
	}
}

// PublicServerListResponse for returning a list of public server data.
type PublicServerListResponse struct {
	Servers []PublicServerResponse `json:"servers"`
}
