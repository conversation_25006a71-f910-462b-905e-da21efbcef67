package announcement

import (
	"time"

	"gorm.io/gorm"
)

// Announcement represents the model for an announcement in the database.
type Announcement struct {
	gorm.Model
	Judul string `json:"judul" gorm:"not null"`
	Isi   string `json:"isi" gorm:"not null"`
}

// AnnouncementResponse defines the structure for API responses for an announcement.
// This avoids exposing the gorm.Model directly and resolves swagger parsing issues.
type AnnouncementResponse struct {
	ID        uint      `json:"id"`
	Judul     string    `json:"judul"`
	Isi       string    `json:"isi"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
