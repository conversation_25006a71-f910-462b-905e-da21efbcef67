package account

import (
	"time"
	"vpn-shop/backend-go/models/server"

	"gorm.io/gorm"
)

// AnyAccount is a generic struct to hold common account information
// from different account tables (Trojan, Vmess, Vless) for unified processing.
type AnyAccount struct {
	ID          uint           `json:"id"`
	Username    string         `json:"username"`
	Password    string         `json:"password"`
	AccountType string         `json:"account_type"`
	Status      string         `json:"status"`
	ExpiredDate *time.Time     `json:"expired_date"`
	ServerID    uint           `json:"server_id"`
	UserID      string         `json:"user_id"`
	Server      server.Server  `json:"server"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `gorm:"index" json:"-"`
}
