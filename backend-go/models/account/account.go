package account

import (
	"strconv"
	"time"
	"vpn-shop/backend-go/models/server"

	"gorm.io/gorm"
)

// IAccount adalah interface untuk semua struct akun
type IAccount interface {
	GetID() uint
	GetUserID() string
	GetHarga() string
	GetExpiredDate() *time.Time
	GetUsername() string
	GetKodeServer() string
	GetPassword() string
	GetUUID() string
	GetKodeAkun() string
	GetDomain() string
	GetDurasi() string
	GetTanggalBeli() time.Time
	GetStatus() string
	GetSubscriptionType() string
	GetCreatedAt() time.Time
	GetUpdatedAt() time.Time
	GetIsHourly() bool
	GetLastStart() *time.Time
	GetTotalHours() int
}

// AccountTrojan represents the account_trojans table
type AccountTrojan struct {
	Server           server.Server  `gorm:"foreignKey:KodeServer;references:Kode" json:"server"`
	AccountID        uint           `gorm:"primaryKey;autoIncrement;column:id" json:"account_id"`
	UserID           string         `json:"user_id"`
	KodeServer       string         `json:"kode_server"`
	KodeAkun         string         `gorm:"index" json:"kode_akun"`
	Domain           string         `json:"domain"`
	Durasi           string         `json:"durasi"`
	Username         string         `json:"username"`
	UUID             string         `json:"uuid"`
	Expired          *time.Time     `gorm:"type:timestamp without time zone" json:"expired,omitempty"`
	TanggalBeli      time.Time      `gorm:"type:timestamp without time zone;not null" json:"tanggal_beli"`
	Status           string         `json:"status"`
	IsHourly         bool           `gorm:"default:0" json:"is_hourly"`
	LastStart        *time.Time     `gorm:"type:timestamp without time zone" json:"last_start"`
	UnbilledMinutes  int            `gorm:"default:0" json:"unbilled_minutes"`
	TotalHours       int            `gorm:"default:0" json:"total_hours"`
	LastBilledAt     *time.Time     `gorm:"type:timestamp without time zone" json:"last_billed_at,omitempty"`
	SubscriptionType string         `json:"subscription_type"`
	Harga            string         `json:"harga"`
	CreatedAt        time.Time      `gorm:"type:timestamp without time zone" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"type:timestamp without time zone" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index;type:timestamp without time zone" json:"deleted_at" swaggertype:"string" format:"date-time"`
}

// Implementasi IAccount untuk AccountTrojan
func (a *AccountTrojan) GetID() uint                 { return a.AccountID }
func (a *AccountTrojan) GetUserID() string           { return a.UserID }
func (a *AccountTrojan) GetHarga() string            { return a.Harga }
func (a *AccountTrojan) GetExpiredDate() *time.Time  { return a.Expired }
func (a *AccountTrojan) GetUsername() string         { return a.Username }
func (a *AccountTrojan) GetKodeServer() string       { return a.KodeServer }
func (a *AccountTrojan) GetPassword() string         { return "" /* Trojan uses UUID */ }
func (a *AccountTrojan) GetUUID() string             { return a.UUID }
func (a *AccountTrojan) GetKodeAkun() string         { return a.KodeAkun }
func (a *AccountTrojan) GetDomain() string           { return a.Domain }
func (a *AccountTrojan) GetDurasi() string           { return a.Durasi }
func (a *AccountTrojan) GetTanggalBeli() time.Time   { return a.TanggalBeli }
func (a *AccountTrojan) GetStatus() string           { return a.Status }
func (a *AccountTrojan) GetSubscriptionType() string { return a.SubscriptionType }
func (a *AccountTrojan) GetCreatedAt() time.Time     { return a.CreatedAt }
func (a *AccountTrojan) GetUpdatedAt() time.Time     { return a.UpdatedAt }
func (a *AccountTrojan) GetIsHourly() bool           { return a.IsHourly }
func (a *AccountTrojan) GetLastStart() *time.Time    { return a.LastStart }
func (a *AccountTrojan) GetTotalHours() int          { return a.TotalHours }

// GetHargaAsInt64 converts the Harga string to int64 for calculations.
func (a *AccountTrojan) GetHargaAsInt64() (int64, error) {
	return strconv.ParseInt(a.Harga, 10, 64)
}

func (a *AccountTrojan) TableName() string {
	return "account_trojans"
}

// AccountVmess represents the account_vmess table
type AccountVmess struct {
	Server           server.Server  `gorm:"foreignKey:KodeServer;references:Kode" json:"server"`
	AccountID        uint           `gorm:"primaryKey;autoIncrement;column:id" json:"account_id"`
	UserID           string         `json:"user_id"`
	KodeServer       string         `json:"kode_server"`
	KodeAkun         string         `gorm:"index" json:"kode_akun"`
	Domain           string         `json:"domain"`
	Durasi           string         `json:"durasi"`
	Username         string         `json:"username"`
	UUID             string         `json:"uuid"`
	Expired          *time.Time     `gorm:"type:timestamp without time zone" json:"expired,omitempty"`
	TanggalBeli      time.Time      `gorm:"type:timestamp without time zone;not null" json:"tanggal_beli"`
	Status           string         `json:"status"`
	IsHourly         bool           `gorm:"default:0" json:"is_hourly"`
	LastStart        *time.Time     `gorm:"type:timestamp without time zone" json:"last_start"`
	UnbilledMinutes  int            `gorm:"default:0" json:"unbilled_minutes"`
	TotalHours       int            `gorm:"default:0" json:"total_hours"`
	LastBilledAt     *time.Time     `gorm:"type:timestamp without time zone" json:"last_billed_at,omitempty"`
	SubscriptionType string         `json:"subscription_type"`
	Harga            string         `json:"harga"`
	CreatedAt        time.Time      `gorm:"type:timestamp without time zone" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"type:timestamp without time zone" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index;type:timestamp without time zone" json:"deleted_at" swaggertype:"string" format:"date-time"`
}

// Implementasi IAccount untuk AccountVmess
func (a *AccountVmess) GetID() uint                 { return a.AccountID }
func (a *AccountVmess) GetUserID() string           { return a.UserID }
func (a *AccountVmess) GetHarga() string            { return a.Harga }
func (a *AccountVmess) GetExpiredDate() *time.Time  { return a.Expired }
func (a *AccountVmess) GetUsername() string         { return a.Username }
func (a *AccountVmess) GetKodeServer() string       { return a.KodeServer }
func (a *AccountVmess) GetPassword() string         { return "" /* Vmess uses UUID */ }
func (a *AccountVmess) GetUUID() string             { return a.UUID }
func (a *AccountVmess) GetKodeAkun() string         { return a.KodeAkun }
func (a *AccountVmess) GetDomain() string           { return a.Domain }
func (a *AccountVmess) GetDurasi() string           { return a.Durasi }
func (a *AccountVmess) GetTanggalBeli() time.Time   { return a.TanggalBeli }
func (a *AccountVmess) GetStatus() string           { return a.Status }
func (a *AccountVmess) GetSubscriptionType() string { return a.SubscriptionType }
func (a *AccountVmess) GetCreatedAt() time.Time     { return a.CreatedAt }
func (a *AccountVmess) GetUpdatedAt() time.Time     { return a.UpdatedAt }
func (a *AccountVmess) GetIsHourly() bool           { return a.IsHourly }
func (a *AccountVmess) GetLastStart() *time.Time    { return a.LastStart }
func (a *AccountVmess) GetTotalHours() int          { return a.TotalHours }

// GetHargaAsInt64 converts the Harga string to int64 for calculations.
func (a *AccountVmess) GetHargaAsInt64() (int64, error) {
	return strconv.ParseInt(a.Harga, 10, 64)
}

func (a *AccountVmess) TableName() string {
	return "account_vmess"
}

// AccountVless represents the account_vless table
type AccountVless struct {
	Server           server.Server  `gorm:"foreignKey:KodeServer;references:Kode" json:"server"`
	AccountID        uint           `gorm:"primaryKey;autoIncrement;column:id" json:"account_id"`
	UserID           string         `json:"user_id"`
	KodeServer       string         `json:"kode_server"`
	KodeAkun         string         `gorm:"index" json:"kode_akun"`
	Domain           string         `json:"domain"`
	Durasi           string         `json:"durasi"`
	Username         string         `json:"username"`
	UUID             string         `json:"uuid"`
	Expired          *time.Time     `gorm:"type:timestamp without time zone" json:"expired,omitempty"`
	TanggalBeli      time.Time      `gorm:"type:timestamp without time zone;not null" json:"tanggal_beli"`
	Status           string         `json:"status"`
	IsHourly         bool           `gorm:"default:0" json:"is_hourly"`
	LastStart        *time.Time     `gorm:"type:timestamp without time zone" json:"last_start"`
	UnbilledMinutes  int            `gorm:"default:0" json:"unbilled_minutes"`
	TotalHours       int            `gorm:"default:0" json:"total_hours"`
	LastBilledAt     *time.Time     `gorm:"type:timestamp without time zone" json:"last_billed_at,omitempty"`
	SubscriptionType string         `json:"subscription_type"`
	Harga            string         `json:"harga"`
	CreatedAt        time.Time      `gorm:"type:timestamp without time zone" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"type:timestamp without time zone" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index;type:timestamp without time zone" json:"deleted_at" swaggertype:"string" format:"date-time"`
}

// Implementasi IAccount untuk AccountVless
func (a *AccountVless) GetID() uint                 { return a.AccountID }
func (a *AccountVless) GetUserID() string           { return a.UserID }
func (a *AccountVless) GetHarga() string            { return a.Harga }
func (a *AccountVless) GetExpiredDate() *time.Time  { return a.Expired }
func (a *AccountVless) GetUsername() string         { return a.Username }
func (a *AccountVless) GetKodeServer() string       { return a.KodeServer }
func (a *AccountVless) GetPassword() string         { return "" /* Vless uses UUID */ }
func (a *AccountVless) GetUUID() string             { return a.UUID }
func (a *AccountVless) GetKodeAkun() string         { return a.KodeAkun }
func (a *AccountVless) GetDomain() string           { return a.Domain }
func (a *AccountVless) GetDurasi() string           { return a.Durasi }
func (a *AccountVless) GetTanggalBeli() time.Time   { return a.TanggalBeli }
func (a *AccountVless) GetStatus() string           { return a.Status }
func (a *AccountVless) GetSubscriptionType() string { return a.SubscriptionType }
func (a *AccountVless) GetCreatedAt() time.Time     { return a.CreatedAt }
func (a *AccountVless) GetUpdatedAt() time.Time     { return a.UpdatedAt }
func (a *AccountVless) GetIsHourly() bool           { return a.IsHourly }
func (a *AccountVless) GetLastStart() *time.Time    { return a.LastStart }
func (a *AccountVless) GetTotalHours() int          { return a.TotalHours }

// GetHargaAsInt64 converts the Harga string to int64 for calculations.
func (a *AccountVless) GetHargaAsInt64() (int64, error) {
	return strconv.ParseInt(a.Harga, 10, 64)
}

func (a *AccountVless) TableName() string {
	return "account_vless"
}

// AccountSsh represents the account_ssh table
type AccountSsh struct {
	AccountID        uint           `gorm:"primaryKey;autoIncrement;column:id" json:"account_id"`
	UserID           string         `json:"user_id"`
	KodeServer       string         `json:"kode_server"`
	KodeAkun         string         `gorm:"index" json:"kode_akun"`
	Domain           string         `json:"domain"`
	Durasi           string         `json:"durasi"`
	Username         string         `json:"username"`
	Password         string         `json:"password"`
	Expired          *time.Time     `gorm:"type:timestamp without time zone" json:"expired,omitempty"`
	TanggalBeli      time.Time      `gorm:"type:timestamp without time zone;not null" json:"tanggal_beli"`
	Status           string         `json:"status"`
	IsHourly         bool           `gorm:"default:0" json:"is_hourly"`
	LastStart        *time.Time     `gorm:"type:timestamp without time zone" json:"last_start"`
	UnbilledMinutes  int            `gorm:"default:0" json:"unbilled_minutes"`
	TotalHours       int            `gorm:"default:0" json:"total_hours"`
	LastBilledAt     *time.Time     `gorm:"type:timestamp without time zone" json:"last_billed_at,omitempty"`
	SubscriptionType string         `json:"subscription_type"`
	Harga            string         `json:"harga"`
	CreatedAt        time.Time      `gorm:"type:timestamp without time zone" json:"created_at"`
	UpdatedAt        time.Time      `gorm:"type:timestamp without time zone" json:"updated_at"`
	DeletedAt        gorm.DeletedAt `gorm:"index;type:timestamp without time zone" json:"deleted_at" swaggertype:"string" format:"date-time"`
}

// Implementasi IAccount untuk AccountSsh
func (a *AccountSsh) GetID() uint                 { return a.AccountID }
func (a *AccountSsh) GetUserID() string           { return a.UserID }
func (a *AccountSsh) GetHarga() string            { return a.Harga }
func (a *AccountSsh) GetExpiredDate() *time.Time  { return a.Expired }
func (a *AccountSsh) GetUsername() string         { return a.Username }
func (a *AccountSsh) GetKodeServer() string       { return a.KodeServer }
func (a *AccountSsh) GetPassword() string         { return a.Password }
func (a *AccountSsh) GetUUID() string             { return "" /* SSH uses password */ }
func (a *AccountSsh) GetKodeAkun() string         { return a.KodeAkun }
func (a *AccountSsh) GetDomain() string           { return a.Domain }
func (a *AccountSsh) GetDurasi() string           { return a.Durasi }
func (a *AccountSsh) GetTanggalBeli() time.Time   { return a.TanggalBeli }
func (a *AccountSsh) GetStatus() string           { return a.Status }
func (a *AccountSsh) GetSubscriptionType() string { return a.SubscriptionType }
func (a *AccountSsh) GetCreatedAt() time.Time     { return a.CreatedAt }
func (a *AccountSsh) GetUpdatedAt() time.Time     { return a.UpdatedAt }
func (a *AccountSsh) GetIsHourly() bool           { return a.IsHourly }
func (a *AccountSsh) GetLastStart() *time.Time    { return a.LastStart }
func (a *AccountSsh) GetTotalHours() int          { return a.TotalHours }

// GetHargaAsInt64 converts the Harga string to int64 for calculations.
func (a *AccountSsh) GetHargaAsInt64() (int64, error) {
	return strconv.ParseInt(a.Harga, 10, 64)
}

func (a *AccountSsh) TableName() string {
	return "account_ssh"
}
