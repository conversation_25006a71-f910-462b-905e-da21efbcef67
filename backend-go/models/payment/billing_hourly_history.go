package payment

import (
	"time"

	"gorm.io/gorm"
)

// BillingHourlyHistory records each instance of hourly billing for a user's account.
//
// @Description BillingHourlyHistory contains the details of each hourly charge, linking users, accounts, and the amount billed.
// @Swagger:model BillingHourlyHistory
type BillingHourlyHistory struct {
	ID               uint           `gorm:"primarykey"`
	CreatedAt        time.Time      `gorm:"type:timestamp without time zone"`
	UpdatedAt        time.Time      `gorm:"type:timestamp without time zone"`
	DeletedAt        gorm.DeletedAt `gorm:"index;type:timestamp without time zone"`
	AccountID        uint           `gorm:"not null;index" json:"account_id"`
	AccountType      string         `gorm:"not null;index" json:"account_type"`                               // e.g., 'trojan', 'vmess', 'vless'
	UserID           string         `gorm:"not null;index" json:"user_id"`                                    // Foreign key to User
	AmountBilled     int64          `gorm:"not null" json:"amount_billed"`                                    // The amount billed in this cycle
	InitialBalance   int64          `gorm:"not null" json:"initial_balance"`                                  // User's balance before this transaction
	RemainingBalance int64          `gorm:"not null" json:"remaining_balance"`                                // User's balance after this transaction
	BilledAt         time.Time      `gorm:"not null;index;type:timestamp without time zone" json:"billed_at"` // The exact time the billing occurred
	HoursBilled      int            `gorm:"not null" json:"hours_billed"`                                     // Number of hours covered in this billing
}
