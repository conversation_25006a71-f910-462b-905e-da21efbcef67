package user

import (
	"time"

	"gorm.io/gorm"
)

// UserResponse adalah struktur data yang aman untuk dikirim ke frontend.
// Tidak mengandung password atau data sensitif lainnya.
type UserResponse struct {
	ID       uint      `json:"id"`
	Name     string    `json:"name"`
	Username string    `json:"username"`
	Email    *string   `json:"email"`
	Whatsapp *string   `json:"whatsapp"`
	Saldo    int64     `json:"saldo"`
	Roles    []string  `json:"roles"`
	Telegram *string   `json:"telegram,omitempty"`
	PhotoURL *string   `json:"photo_url,omitempty"`
}

type User struct {
	ID                 uint           `gorm:"primarykey"`
	CreatedAt          time.Time      `gorm:"type:timestamp without time zone"`
	UpdatedAt          time.Time      `gorm:"type:timestamp without time zone"`
	DeletedAt          gorm.DeletedAt `gorm:"index;type:timestamp without time zone" swaggertype:"string" format:"date-time"`
	Name               string         `gorm:"type:varchar(255);uniqueIndex;not null"`
	Username           string         `gorm:"type:varchar(100);uniqueIndex" json:"username"`
	Email              *string        `gorm:"type:varchar(255);uniqueIndex"`
	Password           string         `json:"-" gorm:"not null"`
	Saldo              int64          `json:"saldo" gorm:"type:numeric(15,0);default:0"`
	VerifWa            int            `gorm:"type:integer;not null;default:0"`
	Telegram           *string        `gorm:"type:varchar(255)"`
	UserTelegram       *string        `gorm:"type:varchar(255);uniqueIndex"`
	Whatsapp           *string        `gorm:"type:varchar(20);uniqueIndex"`
	Otp                *int           `gorm:"type:integer"`
	Suspend            *string        `gorm:"type:varchar(255)"`
	BatasTrial         *int           `gorm:"default:5"`
	Trial              *int           `gorm:"default:0"`
	PayHarian          int64          `gorm:"type:numeric(15,0);default:0"`
	PayMingguan        int64          `gorm:"type:numeric(15,0);default:0"`
	PayBulanan         int64          `gorm:"type:numeric(15,0);default:0"`
	PayPerjam          int64          `gorm:"type:numeric(15,0);default:0"`
	TotalPay           int64          `gorm:"type:numeric(15,0);default:0"`
	Laba               *int64         `gorm:"type:numeric(15,0)"`
	PathPhoto          *string        `gorm:"type:text"`
	EmailVerified      int            `gorm:"type:integer;not null;default:0"`
	EmailVerifiedAt    *time.Time     `gorm:"type:timestamp without time zone"`
	LastLogin          *time.Time     `gorm:"type:timestamp without time zone"`
	LimitDeleteHourly  *int           `gorm:"default:3"`
	DeleteHourlyCount  *int           `gorm:"default:0"`
	LastDeleteHourlyAt *time.Time     `gorm:"type:timestamp without time zone"`
	NotifLogin         *int
	Roles              []*Role `gorm:"many2many:user_roles;"`
}

// GetRoleNames mengembalikan slice dari nama peran untuk pengguna.
func (u *User) GetRoleNames() []string {
	var roleNames []string
	if u.Roles == nil {
		return []string{}
	}
	for _, role := range u.Roles {
		roleNames = append(roleNames, role.Name)
	}
	return roleNames
}

// ToUserResponse mengubah User model menjadi UserResponse yang aman untuk dikirim ke client.
func (u *User) ToUserResponse() UserResponse {
	return UserResponse{
		ID:       u.ID,
		Name:     u.Name,
		Username: u.Username,
		Email:    u.Email,
		Whatsapp: u.Whatsapp,
		Saldo:    u.Saldo,
		Roles:    u.GetRoleNames(),
		Telegram: u.Telegram,
		PhotoURL: u.PathPhoto,
	}
}
