#!/bin/bash

# Script untuk testing endpoint /users/me dengan foto profil
# Pastikan server sudah berjalan sebelum menjalankan script ini

# URL endpoint untuk backend Go
LOGIN_URL="http://localhost:8000/api/v1/auth/login"
PROFILE_URL="http://localhost:8000/api/v1/users/me"
UPLOAD_URL="http://localhost:8000/api/v1/users/me/photo"
HEADERS="Content-Type: application/json"

# Warna untuk output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Testing Profile Photo Integration ===${NC}"

# Cek apakah server berjalan
echo -e "${YELLOW}Checking if server is running...${NC}"
if ! curl -s http://localhost:8000/ > /dev/null; then
    echo -e "${RED}Error: Server tidak berjalan di localhost:8000${NC}"
    echo -e "${YELLOW}Jalankan server terlebih dahulu dengan: go run main.go${NC}"
    exit 1
fi

echo -e "${GREEN}Server is running!${NC}"

# Login untuk mendapatkan token
echo -e "${YELLOW}Login sebagai admin untuk mendapatkan token...${NC}"
LOGIN_DATA=$(cat <<EOF
{
    "identifier": "admin",
    "password": "admin"
}
EOF
)

login_response=$(curl -s -w "\n%{http_code}" -X POST "$LOGIN_URL" \
     -H "$HEADERS" \
     -d "$LOGIN_DATA")

login_http_code=$(tail -n1 <<< "$login_response")
login_body=$(sed '$ d' <<< "$login_response")

if [ "$login_http_code" -ne 200 ]; then
    echo -e "${RED}Login gagal! Status: $login_http_code${NC}"
    exit 1
fi

# Ekstrak token dari response
TOKEN=$(echo $login_body | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo -e "${RED}Gagal mendapatkan token dari response.${NC}"
    exit 1
fi

echo -e "${GREEN}Login berhasil! Token diperoleh.${NC}"

# Test 1: Cek profile sebelum upload
echo -e "\n${YELLOW}Test 1: Get profile before upload${NC}"
profile_response=$(curl -s -w "\n%{http_code}" -X GET "$PROFILE_URL" \
  -H "Authorization: Bearer $TOKEN")

profile_http_code=$(tail -n1 <<< "$profile_response")
profile_body=$(sed '$ d' <<< "$profile_response")

if [ "$profile_http_code" = "200" ]; then
    echo -e "${GREEN}✓ Profile endpoint working${NC}"
    echo -e "${YELLOW}Response:${NC}"
    echo "$profile_body" | jq '.'
    
    # Cek apakah ada photo_url
    photo_url=$(echo "$profile_body" | jq -r '.photo_url // empty')
    if [ -z "$photo_url" ] || [ "$photo_url" = "null" ]; then
        echo -e "${YELLOW}No photo_url found (expected for first time)${NC}"
    else
        echo -e "${GREEN}Photo URL found: $photo_url${NC}"
    fi
else
    echo -e "${RED}✗ Profile endpoint failed: $profile_http_code${NC}"
    echo "$profile_body"
    exit 1
fi

# Test 2: Upload foto
echo -e "\n${YELLOW}Test 2: Upload photo${NC}"

# Buat file test image sederhana (1x1 pixel PNG)
echo -n -e '\x89\x50\x4e\x47\x0d\x0a\x1a\x0a\x00\x00\x00\x0d\x49\x48\x44\x52\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90\x77\x53\xde\x00\x00\x00\x0c\x49\x44\x41\x54\x08\x99\x01\x01\x00\x00\xff\xff\x00\x00\x00\x02\x00\x01\x73\x75\x01\x18\x00\x00\x00\x00\x49\x45\x4e\x44\xae\x42\x60\x82' > test_image.png

upload_response=$(curl -s -w "\n%{http_code}" -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -F "photo=@test_image.png" \
  "$UPLOAD_URL")

upload_http_code=$(tail -n1 <<< "$upload_response")
upload_body=$(sed '$ d' <<< "$upload_response")

if [ "$upload_http_code" = "200" ]; then
    echo -e "${GREEN}✓ Upload successful!${NC}"
    echo -e "${YELLOW}Upload Response:${NC}"
    echo "$upload_body" | jq '.'
else
    echo -e "${RED}✗ Upload failed: $upload_http_code${NC}"
    echo "$upload_body"
    rm -f test_image.png
    exit 1
fi

# Test 3: Cek profile setelah upload
echo -e "\n${YELLOW}Test 3: Get profile after upload${NC}"
profile_response2=$(curl -s -w "\n%{http_code}" -X GET "$PROFILE_URL" \
  -H "Authorization: Bearer $TOKEN")

profile_http_code2=$(tail -n1 <<< "$profile_response2")
profile_body2=$(sed '$ d' <<< "$profile_response2")

if [ "$profile_http_code2" = "200" ]; then
    echo -e "${GREEN}✓ Profile endpoint working after upload${NC}"
    echo -e "${YELLOW}Response:${NC}"
    echo "$profile_body2" | jq '.'
    
    # Cek apakah ada photo_url
    photo_url2=$(echo "$profile_body2" | jq -r '.photo_url // empty')
    if [ -z "$photo_url2" ] || [ "$photo_url2" = "null" ]; then
        echo -e "${RED}✗ No photo_url found after upload!${NC}"
    else
        echo -e "${GREEN}✓ Photo URL found after upload: $photo_url2${NC}"
        
        # Test akses foto
        echo -e "\n${YELLOW}Test 4: Access uploaded photo${NC}"
        photo_access_response=$(curl -s -w "%{http_code}" -o /dev/null "http://localhost:8000/$photo_url2")
        if [ "$photo_access_response" = "200" ]; then
            echo -e "${GREEN}✓ Photo accessible at: http://localhost:8000/$photo_url2${NC}"
        else
            echo -e "${RED}✗ Photo not accessible: HTTP $photo_access_response${NC}"
        fi
    fi
else
    echo -e "${RED}✗ Profile endpoint failed after upload: $profile_http_code2${NC}"
    echo "$profile_body2"
fi

# Cleanup
rm -f test_image.png

echo -e "\n${YELLOW}=== Testing Complete ===${NC}"
echo -e "${GREEN}Profile photo integration test finished!${NC}"
