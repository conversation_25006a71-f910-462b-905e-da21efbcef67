package utils

import (
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
)

// CopyDefaultAvatar menyalin avatar default ke direktori uploads untuk user baru
func CopyDefaultAvatar(userID uint) (string, error) {
	// Path ke file avatar default
	defaultAvatarPath := "assets/default-avatar.svg"
	
	// Pastikan direktori uploads/photos ada
	uploadsDir := "uploads/photos"
	if err := os.MkdirAll(uploadsDir, 0755); err != nil {
		return "", fmt.Errorf("gagal membuat direktori uploads: %v", err)
	}
	
	// Nama file untuk user
	fileName := fmt.Sprintf("user_%d_default.svg", userID)
	destPath := filepath.Join(uploadsDir, fileName)
	
	// Buka file sumber
	srcFile, err := os.Open(defaultAvatarPath)
	if err != nil {
		return "", fmt.Errorf("gagal membuka file avatar default: %v", err)
	}
	defer srcFile.Close()
	
	// Buat file tujuan
	destFile, err := os.Create(destPath)
	if err != nil {
		return "", fmt.Errorf("gagal membuat file avatar: %v", err)
	}
	defer destFile.Close()
	
	// Salin konten
	_, err = io.Copy(destFile, srcFile)
	if err != nil {
		return "", fmt.Errorf("gagal menyalin file avatar: %v", err)
	}
	
	// Return path relatif untuk disimpan di database
	return strings.Replace(destPath, "\\", "/", -1), nil
}

// GenerateColoredAvatar membuat avatar SVG dengan warna berdasarkan nama user
func GenerateColoredAvatar(userID uint, userName string) (string, error) {
	// Pastikan direktori uploads/photos ada
	uploadsDir := "uploads/photos"
	if err := os.MkdirAll(uploadsDir, 0755); err != nil {
		return "", fmt.Errorf("gagal membuat direktori uploads: %v", err)
	}
	
	// Generate warna berdasarkan nama
	colors := getAvatarColors(userName)
	
	// Nama file untuk user
	fileName := fmt.Sprintf("user_%d_default.svg", userID)
	destPath := filepath.Join(uploadsDir, fileName)
	
	// Template SVG dengan warna dinamis
	svgContent := fmt.Sprintf(`<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%%" y1="0%%" x2="100%%" y2="100%%">
      <stop offset="0%%" style="stop-color:%s;stop-opacity:1" />
      <stop offset="100%%" style="stop-color:%s;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="faceGradient" x1="0%%" y1="0%%" x2="100%%" y2="100%%">
      <stop offset="0%%" style="stop-color:#ffffff;stop-opacity:0.9" />
      <stop offset="100%%" style="stop-color:#f8fafc;stop-opacity:0.8" />
    </linearGradient>
  </defs>
  
  <!-- Background with Gradient -->
  <circle cx="100" cy="100" r="100" fill="url(#avatarGradient)"/>
  
  <!-- User Icon -->
  <g transform="translate(100, 100)">
    <!-- Head -->
    <circle cx="0" cy="-20" r="25" fill="url(#faceGradient)" stroke="#e2e8f0" stroke-width="2"/>
    
    <!-- Body -->
    <path d="M -35 20 Q -35 0 -25 0 L 25 0 Q 35 0 35 20 L 35 50 L -35 50 Z" 
          fill="url(#faceGradient)" stroke="#e2e8f0" stroke-width="2"/>
    
    <!-- Eyes -->
    <circle cx="-8" cy="-25" r="2" fill="#64748b"/>
    <circle cx="8" cy="-25" r="2" fill="#64748b"/>
    
    <!-- Smile -->
    <path d="M -8 -15 Q 0 -10 8 -15" stroke="#64748b" stroke-width="2" fill="none" stroke-linecap="round"/>
    
    <!-- Initial Letter -->
    <text x="0" y="35" text-anchor="middle" font-family="Arial, sans-serif" font-size="20" font-weight="bold" fill="#64748b">%s</text>
  </g>
  
  <!-- Decorative Elements -->
  <circle cx="160" cy="40" r="3" fill="#ffffff" opacity="0.3"/>
  <circle cx="40" cy="160" r="2" fill="#ffffff" opacity="0.4"/>
  <circle cx="170" cy="170" r="2" fill="#ffffff" opacity="0.2"/>
</svg>`, colors.primary, colors.secondary, getInitial(userName))
	
	// Tulis file SVG
	err := os.WriteFile(destPath, []byte(svgContent), 0644)
	if err != nil {
		return "", fmt.Errorf("gagal menulis file avatar: %v", err)
	}
	
	// Return path relatif untuk disimpan di database
	return strings.Replace(destPath, "\\", "/", -1), nil
}

// AvatarColors menyimpan warna untuk avatar
type AvatarColors struct {
	primary   string
	secondary string
}

// getAvatarColors menghasilkan warna berdasarkan nama user
func getAvatarColors(name string) AvatarColors {
	// Daftar kombinasi warna yang bagus
	colorSets := []AvatarColors{
		{primary: "#6366f1", secondary: "#3b82f6"}, // Indigo to Blue
		{primary: "#8b5cf6", secondary: "#6366f1"}, // Purple to Indigo
		{primary: "#06b6d4", secondary: "#0891b2"}, // Cyan to Sky
		{primary: "#10b981", secondary: "#059669"}, // Emerald to Green
		{primary: "#f59e0b", secondary: "#d97706"}, // Amber to Yellow
		{primary: "#ef4444", secondary: "#dc2626"}, // Red to Rose
		{primary: "#ec4899", secondary: "#db2777"}, // Pink to Rose
		{primary: "#84cc16", secondary: "#65a30d"}, // Lime to Green
	}
	
	// Hitung hash sederhana dari nama
	hash := 0
	for _, char := range name {
		hash += int(char)
	}
	
	// Pilih warna berdasarkan hash
	return colorSets[hash%len(colorSets)]
}

// getInitial mengambil huruf pertama dari nama
func getInitial(name string) string {
	if len(name) == 0 {
		return "U"
	}
	
	// Ambil huruf pertama dan ubah ke uppercase
	return strings.ToUpper(string(name[0]))
}
