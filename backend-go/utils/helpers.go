package utils

import (
	"crypto/hmac"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"math/big"
	"reflect"
	"sort"
	"strconv"
	"strings"

	"github.com/go-playground/validator/v10"
	"gorm.io/gorm"

	"vpn-shop/backend-go/models/account"
)

var (
	Validate = validator.New()
)

// FormatValidationErrors formats validation errors into a readable map.
func FormatValidationErrors(err error) map[string]string {
	errors := make(map[string]string)
	if validationErrors, ok := err.(validator.ValidationErrors); ok {
		for _, fieldErr := range validationErrors {
			errors[fieldErr.Field()] = fmt.Sprintf("Error pada field '%s' dengan kondisi '%s'", fieldErr.Field(), fieldErr.Tag())
		}
	}
	return errors
}

// GenerateRandomString creates a cryptographically secure random string of a given length.
func GenerateRandomString(n int) (string, error) {
	const letters = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz"
	ret := make([]byte, n)
	for i := 0; i < n; i++ {
		num, err := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
		if err != nil {
			return "", err
		}
		ret[i] = letters[num.Int64()]
	}
	return string(ret), nil
}

// IsUsernameExists checks if a username already exists for a specific server in the database.
func IsUsernameExists(tx *gorm.DB, username, kodeServer string) (bool, error) {
	var count int64

	// Check in trojan accounts
	if err := tx.Model(&account.AccountTrojan{}).Where("username = ? AND kode_server = ?", username, kodeServer).Count(&count).Error; err != nil {
		return false, err
	}
	if count > 0 {
		return true, nil
	}

	// Check in vmess accounts
	if err := tx.Model(&account.AccountVmess{}).Where("username = ? AND kode_server = ?", username, kodeServer).Count(&count).Error; err != nil {
		return false, err
	}
	if count > 0 {
		return true, nil
	}

	// Check in vless accounts
	if err := tx.Model(&account.AccountVless{}).Where("username = ? AND kode_server = ?", username, kodeServer).Count(&count).Error; err != nil {
		return false, err
	}
	if count > 0 {
		return true, nil
	}

	return false, nil
}

// FormatRupiah mengubah nilai int64 menjadi format string mata uang Rupiah.
func FormatRupiah(amount int64) string {
	s := strconv.FormatInt(amount, 10)
	length := len(s)
	if length <= 3 {
		return fmt.Sprintf("Rp %s", s)
	}

	// Hitung jumlah pemisah (titik)
	separators := (length - 1) / 3
	// Hitung panjang string hasil
	resultLength := length + separators

	result := make([]byte, resultLength)
	// Salin dari belakang untuk menempatkan titik
	pos := resultLength - 1
	for i, j := length-1, 0; i >= 0; i, j = i-1, j+1 {
		if j > 0 && j%3 == 0 {
			result[pos] = '.'
			pos--
		}
		result[pos] = s[i]
		pos--
	}

	return fmt.Sprintf("Rp %s", string(result))
}

// IntToStringPointer mengubah integer menjadi pointer string dengan format "N bulan".
// Berguna untuk menyimpan durasi ke dalam model Transaction.
func IntToStringPointer(months int) *string {
	durationStr := fmt.Sprintf("%d bulan", months)
	return &durationStr
}

// StringToPointer mengubah string literal menjadi pointer string.
func StringToPointer(s string) *string {
	return &s
}

// Int64ToString mengubah nilai int64 menjadi representasi string-nya.
func Int64ToString(n int64) string {
	return strconv.FormatInt(n, 10)
}

// VerifyTelegramHash checks if the hash received from Telegram is valid.
func VerifyTelegramHash(hash string, data interface{}, botToken string) bool {
	// 1. Create a data-check-string
	var dataCheckString string
	// Use reflection to build the string in alphabetical order of keys
	v := reflect.ValueOf(data)
	typeOfS := v.Type()

	var pairs []string
	for i := 0; i < v.NumField(); i++ {
		field := typeOfS.Field(i)
		fieldName := field.Tag.Get("json")
		fieldValue := v.Field(i).Interface()

		// The 'hash' field is not part of the data-check-string
		if fieldName == "hash" {
			continue
		}

		// Convert value to string and append
		valStr := fmt.Sprintf("%v", fieldValue)
		if valStr != "" && valStr != "0" { // Ignore empty or zero values as per Telegram's spec
			pairs = append(pairs, fmt.Sprintf("%s=%s", fieldName, valStr))
		}
	}
	sort.Strings(pairs)
	dataCheckString = strings.Join(pairs, "\n")

	// 2. Create a secret key by hashing the bot token
	secretKey := sha256.Sum256([]byte(botToken))

	// 3. Calculate HMAC-SHA256 of the data-check-string with the secret key
	mac := hmac.New(sha256.New, secretKey[:])
	mac.Write([]byte(dataCheckString))
	calculatedHash := hex.EncodeToString(mac.Sum(nil))

	// 4. Compare the calculated hash with the one from Telegram
	return calculatedHash == hash
}
