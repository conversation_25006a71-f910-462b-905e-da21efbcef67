#!/bin/bash

# Script untuk testing endpoint upload foto
# Pastikan server sudah berjalan sebelum menjalankan script ini

# URL endpoint untuk backend Go
LOGIN_URL="http://localhost:8000/api/v1/auth/login"
UPLOAD_URL="http://localhost:8000/api/v1/users/me/photo"
HEADERS="Content-Type: application/json"

# Warna untuk output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo -e "${YELLOW}=== Testing Upload Photo Endpoint ===${NC}"
echo "PASTIKAN SERVER SUDAH BERJALAN DENGAN: cd backend-go && go run main.go"

# Cek apakah server berjalan
echo -e "${YELLOW}Checking if server is running...${NC}"
if ! curl -s http://localhost:8000/ > /dev/null; then
    echo -e "${RED}Error: Server tidak berjalan di localhost:8000${NC}"
    echo -e "${YELLOW}Jalankan server terlebih dahulu dengan: go run main.go${NC}"
    exit 1
fi

echo -e "${GREEN}Server is running!${NC}"

# Login untuk mendapatkan token
echo -e "${YELLOW}Login sebagai admin untuk mendapatkan token...${NC}"
LOGIN_DATA=$(cat <<EOF
{
    "identifier": "admin",
    "password": "admin"
}
EOF
)

login_response=$(curl -s -w "\n%{http_code}" -X POST "$LOGIN_URL" \
     -H "$HEADERS" \
     -d "$LOGIN_DATA")

login_http_code=$(tail -n1 <<< "$login_response")
login_body=$(sed '$ d' <<< "$login_response")

if [ "$login_http_code" -ne 200 ]; then
    echo -e "${RED}Login gagal! Status: $login_http_code, Detail: $login_body${NC}"
    echo -e "${YELLOW}Pastikan server berjalan dan user admin ada.${NC}"
    exit 1
fi

# Ekstrak token dari response
TOKEN=$(echo $login_body | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo -e "${RED}Gagal mendapatkan token dari response.${NC}"
    exit 1
fi

echo -e "${GREEN}Login berhasil! Token diperoleh.${NC}"

# Buat file test image sederhana (1x1 pixel PNG)
echo -e "${YELLOW}Creating test image...${NC}"
echo -n -e '\x89\x50\x4e\x47\x0d\x0a\x1a\x0a\x00\x00\x00\x0d\x49\x48\x44\x52\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90\x77\x53\xde\x00\x00\x00\x0c\x49\x44\x41\x54\x08\x99\x01\x01\x00\x00\xff\xff\x00\x00\x00\x02\x00\x01\x73\x75\x01\x18\x00\x00\x00\x00\x49\x45\x4e\x44\xae\x42\x60\x82' > test_image.png

echo -e "${GREEN}Test image created: test_image.png${NC}"

# Test dengan token valid (harus berhasil)
echo -e "\n${YELLOW}Test 1: Upload with valid token (should succeed)${NC}"
upload_response=$(curl -s -w "\n%{http_code}" -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -F "photo=@test_image.png" \
  "$UPLOAD_URL")

upload_http_code=$(tail -n1 <<< "$upload_response")
upload_body=$(sed '$ d' <<< "$upload_response")

if [ "$upload_http_code" = "200" ]; then
    echo -e "${GREEN}✓ Test 1 passed: Upload berhasil!${NC}"
    echo -e "${GREEN}Response: $upload_body${NC}"
else
    echo -e "${RED}✗ Test 1 failed: Expected 200, got $upload_http_code${NC}"
    echo -e "${RED}Response: $upload_body${NC}"
fi

# Test tanpa token (harus gagal)
echo -e "\n${YELLOW}Test 2: Upload without token (should fail)${NC}"
response=$(curl -s -w "%{http_code}" -X POST \
  -F "photo=@test_image.png" \
  "$UPLOAD_URL")

http_code="${response: -3}"
if [ "$http_code" = "401" ] || [ "$http_code" = "400" ]; then
    echo -e "${GREEN}✓ Test 2 passed: Correctly rejected request without token (HTTP $http_code)${NC}"
else
    echo -e "${RED}✗ Test 2 failed: Expected 401 or 400, got $http_code${NC}"
fi

# Test dengan token invalid (harus gagal)
echo -e "\n${YELLOW}Test 3: Upload with invalid token (should fail)${NC}"
response=$(curl -s -w "%{http_code}" -X POST \
  -H "Authorization: Bearer invalid_token" \
  -F "photo=@test_image.png" \
  "$UPLOAD_URL")

http_code="${response: -3}"
if [ "$http_code" = "401" ]; then
    echo -e "${GREEN}✓ Test 3 passed: Correctly rejected request with invalid token${NC}"
else
    echo -e "${RED}✗ Test 3 failed: Expected 401, got $http_code${NC}"
fi

# Test dengan file yang bukan image (harus gagal)
echo -e "\n${YELLOW}Test 4: Upload non-image file with valid token (should fail)${NC}"
echo "This is not an image" > test_text.txt
response=$(curl -s -w "%{http_code}" -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -F "photo=@test_text.txt" \
  "$UPLOAD_URL")

http_code="${response: -3}"
if [ "$http_code" = "415" ]; then
    echo -e "${GREEN}✓ Test 4 passed: Correctly rejected non-image file${NC}"
else
    echo -e "${RED}✗ Test 4 failed: Expected 415, got $http_code${NC}"
fi

# Test upload kedua untuk memastikan foto lama terhapus
echo -e "\n${YELLOW}Test 5: Upload second photo (should replace first)${NC}"
upload_response2=$(curl -s -w "\n%{http_code}" -X POST \
  -H "Authorization: Bearer $TOKEN" \
  -F "photo=@test_image.png" \
  "$UPLOAD_URL")

upload_http_code2=$(tail -n1 <<< "$upload_response2")
upload_body2=$(sed '$ d' <<< "$upload_response2")

if [ "$upload_http_code2" = "200" ]; then
    echo -e "${GREEN}✓ Test 5 passed: Second upload berhasil!${NC}"
    echo -e "${GREEN}Response: $upload_body2${NC}"
else
    echo -e "${RED}✗ Test 5 failed: Expected 200, got $upload_http_code2${NC}"
    echo -e "${RED}Response: $upload_body2${NC}"
fi

# Cleanup
rm -f test_image.png test_text.txt

echo -e "\n${YELLOW}=== Testing Complete ===${NC}"
echo -e "${GREEN}Endpoint upload foto sudah berfungsi dengan baik!${NC}"
echo -e "${YELLOW}File foto disimpan di: backend-go/uploads/photos/${NC}"
echo -e "${YELLOW}Akses foto via: http://localhost:8000/uploads/photos/filename${NC}"
