# Testing Upload Photo Endpoint

## Endpoint Details
- **URL**: `POST /api/v1/users/me/photo`
- **Authentication**: <PERSON><PERSON> (JWT) required
- **Content-Type**: `multipart/form-data`
- **Parameter**: `photo` (file)

## Validations
1. **File Size**: Maximum 5MB
2. **File Types**: JPEG, PNG, WebP only
3. **Authentication**: Valid JWT token required

## Testing Steps

### 1. Start the Server
```bash
cd backend-go
go run main.go
```

### 2. Get Authentication Token
First, login to get a valid JWT token:
```bash
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"your_username","password":"your_password"}'
```

### 3. Test Upload Photo
```bash
# Replace YOUR_TOKEN with the actual token from login
curl -X POST http://localhost:8080/api/v1/users/me/photo \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -F "photo=@path/to/your/image.jpg"
```

### 4. Expected Responses

#### Success (200 OK)
```json
{
  "message": "Foto profil berhasil diupload",
  "photo_url": "uploads/photos/user_123_1234567890.jpg"
}
```

#### Error Responses

**No Token (401 Unauthorized)**
```json
{
  "error": "Token tidak valid"
}
```

**File Too Large (413 Request Entity Too Large)**
```json
{
  "error": "Ukuran file terlalu besar. Maksimal 5MB"
}
```

**Invalid File Type (415 Unsupported Media Type)**
```json
{
  "error": "Tipe file tidak didukung. Hanya JPEG, PNG, dan WebP yang diizinkan"
}
```

**No File (400 Bad Request)**
```json
{
  "error": "File foto tidak ditemukan"
}
```

## Automated Testing
Run the automated test script:
```bash
./test_upload.sh
```

## File Storage
- Uploaded files are stored in: `backend-go/uploads/photos/`
- File naming pattern: `user_{user_id}_{timestamp}.{extension}`
- Old photos are automatically deleted when new ones are uploaded
- Files are served statically at: `http://localhost:8080/uploads/photos/filename`

## Database Updates
- The `PathPhoto` field in the `users` table is updated with the file path
- The `PhotoURL` field in API responses shows the file path for frontend access

## Security Features
1. File type validation using content detection (not just extension)
2. File size limits
3. JWT authentication required
4. Automatic cleanup of old photos
5. Safe file naming to prevent path traversal attacks
