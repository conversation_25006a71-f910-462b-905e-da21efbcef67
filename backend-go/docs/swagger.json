{"swagger": "2.0", "info": {"description": "This is the API for the VPN Shop application.", "title": "VPN Shop API", "contact": {}, "version": "1.0"}, "host": "localhost:8000", "basePath": "/api/v1", "paths": {"/accounts/detail/{username}/{server_code}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves detailed information for a specific VPN account, combining local data and live data from the Marzban API.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Get account detail", "parameters": [{"type": "string", "description": "Account <PERSON><PERSON><PERSON>", "name": "username", "in": "path", "required": true}, {"type": "string", "description": "Server Code", "name": "server_code", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.AccountDetailResponse"}}, "404": {"description": "Server or account not found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/accounts/history": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of all purchased services (Trojan, Vmess, Vless, SSH) for the logged-in user.", "produces": ["application/json"], "tags": ["Accounts"], "summary": "Get user's service history", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/user.ServiceHistoryResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/accounts/renew": {"post": {"security": [{"OAuth2Password": []}], "description": "Renews a user's VPN account for a specified number of months.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Renew a VPN account", "parameters": [{"description": "Renew Request", "name": "renew_request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/account.RenewRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.RenewSuccessResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "422": {"description": "Unprocessable Entity", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/accounts/{account_id}": {"delete": {"security": [{"OAuth2Password": []}], "description": "Deletes a user's VPN account. Admins can delete any account, while regular users have an hourly limit. For hourly accounts, performs a final billing check.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Delete a user account", "parameters": [{"type": "integer", "description": "Account ID", "name": "account_id", "in": "path", "required": true}, {"enum": ["trojan", "vmess", "vless", "ssh"], "type": "string", "description": "Account Type", "name": "account_type", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.SuccessResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/accounts/{account_id}/activate": {"put": {"security": [{"OAuth2Password": []}], "description": "Sets an account's status to 'active' in both the local database and on the Marzban server.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Activate a user's account", "parameters": [{"type": "string", "description": "Account ID", "name": "account_id", "in": "path", "required": true}, {"enum": ["trojan", "vmess", "vless", "ssh"], "type": "string", "description": "Account Type", "name": "account_type", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.SuccessResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/accounts/{account_id}/disable": {"put": {"security": [{"OAuth2Password": []}], "description": "Sets an account's status to 'disabled' on the Marzban server and 'suspended' in the local database.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Accounts"], "summary": "Disable a user's account", "parameters": [{"type": "string", "description": "Account ID", "name": "account_id", "in": "path", "required": true}, {"enum": ["trojan", "vmess", "vless", "ssh"], "type": "string", "description": "Account Type", "name": "account_type", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.SuccessResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/admin/announcements": {"post": {"security": [{"BearerAuth": []}], "description": "Me<PERSON><PERSON>t pengumuman baru dengan judul dan isi.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin"], "summary": "Create Announcement", "parameters": [{"description": "<PERSON>", "name": "announcement", "in": "body", "required": true, "schema": {"$ref": "#/definitions/admin.CreateAnnouncementRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/announcement.AnnouncementResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/admin/announcements/{id}": {"put": {"security": [{"BearerAuth": []}], "description": "Memperbarui judul atau isi dari pengumuman yang ada berdasarkan ID.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin"], "summary": "Update Announcement", "parameters": [{"type": "integer", "description": "Announcement ID", "name": "id", "in": "path", "required": true}, {"description": "<PERSON> <PERSON><PERSON> yang <PERSON>", "name": "announcement", "in": "body", "required": true, "schema": {"$ref": "#/definitions/admin.UpdateAnnouncementRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/announcement.AnnouncementResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Menghapus pengumuman berdasarkan ID.", "produces": ["application/json"], "tags": ["Admin"], "summary": "Delete Announcement", "parameters": [{"type": "integer", "description": "Announcement ID", "name": "id", "in": "path", "required": true}], "responses": {"204": {"description": "No Content"}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/admin/settings": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves the current application-wide settings.", "produces": ["application/json"], "tags": ["Admin"], "summary": "Get Admin Settings", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/admin.AdminSetting"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Updates the application-wide settings. All fields are required.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin"], "summary": "Update Admin Settings", "parameters": [{"description": "New settings data", "name": "settings", "in": "body", "required": true, "schema": {"$ref": "#/definitions/admin.AdminSetting"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/admin.AdminSetting"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/admin/users": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of all users", "produces": ["application/json"], "tags": ["Admin"], "summary": "Get all users", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/admin.SwaggerUserListResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/admin/users/{id}": {"put": {"security": [{"BearerAuth": []}], "description": "Update a user's details by their ID", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Admin"], "summary": "Update a user", "parameters": [{"type": "integer", "description": "User ID", "name": "id", "in": "path", "required": true}, {"description": "User data to update", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/admin.UpdateUserRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/admin.SwaggerUserDetailResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Soft delete a user by their ID", "produces": ["application/json"], "tags": ["Admin"], "summary": "Delete a user", "parameters": [{"type": "integer", "description": "User ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/auth/login": {"post": {"description": "Authenticate a user and get a token. Used by Swagger's OAuth2 flow.", "consumes": ["application/x-www-form-urlencoded", "application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Log in a user", "parameters": [{"type": "string", "description": "Username or Email", "name": "identifier", "in": "formData", "required": true}, {"type": "string", "description": "Password", "name": "password", "in": "formData", "required": true}], "responses": {"200": {"description": "{\\\"token\\\": \\\"...\\\"}", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/auth/register": {"post": {"description": "Create a new user account with username, email, and password.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Register a new user", "parameters": [{"description": "User registration info", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/auth.RegisterRequest"}}], "responses": {"201": {"description": "Created", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/auth/telegram": {"post": {"description": "Authenticates a user with a Telegram ID. If the user doesn't exist, it creates a new account.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Authenticate or Register a user via Telegram", "parameters": [{"description": "Telegram User auth info", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/auth.TelegramAuthRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "201": {"description": "Created", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/payment": {"post": {"security": [{"BearerAuth": []}], "description": "Creates a new payment transaction for purchasing VPN products.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["payments"], "summary": "Create Product Payment", "parameters": [{"description": "Payment Request", "name": "payment_request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/payment.CreatePaymentRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.TopUpResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/payment/channels": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves a list of available payment channels from the payment gateway.", "produces": ["application/json"], "tags": ["payments"], "summary": "Get Payment Channels", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/payment.TripayChannelsResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/payment/status/{reference}": {"get": {"security": [{"BearerAuth": []}], "description": "Checks the payment status of a transaction using Tripay API", "produces": ["application/json"], "tags": ["payments"], "summary": "Check Payment Status", "parameters": [{"type": "string", "description": "Transaction Reference/Invoice ID", "name": "reference", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/payment.TripayTransactionStatus"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/payment/topup": {"post": {"security": [{"BearerAuth": []}], "description": "Initiates a new top-up transaction for the authenticated user.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["payments"], "summary": "Top-Up Balance", "parameters": [{"description": "Top-Up Request", "name": "topup_request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/payment.TopUpRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.TopUpResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/public/announcements": {"get": {"description": "Men<PERSON><PERSON> daftar semua pengumuman yang tersedia.", "produces": ["application/json"], "tags": ["Public"], "summary": "Get All Announcements", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/announcement.AnnouncementResponse"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/public/announcements/{id}": {"get": {"description": "Mengambil detail satu pengumuman berdasarkan ID-nya.", "produces": ["application/json"], "tags": ["Public"], "summary": "Get Announcement By ID", "parameters": [{"type": "integer", "description": "Announcement ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/announcement.AnnouncementResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/public/stats/top-servers": {"get": {"description": "Mengambil daftar 5 server teratas yang paling banyak dibeli secara global.", "produces": ["application/json"], "tags": ["Public"], "summary": "Get Top Purchase Servers", "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/stats.TopServerResponse"}}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/public/transactions": {"get": {"description": "Mengambil daftar transaksi publik. Tipe bisa 'topup', 'purchase', atau 'trial'.", "produces": ["application/json"], "tags": ["Public"], "summary": "Get Public Transactions", "parameters": [{"enum": ["topup", "purchase", "trial"], "type": "string", "description": "Transaction Type", "name": "type", "in": "query", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "array", "items": {"$ref": "#/definitions/public.TransactionResponse"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/purchase/hourly": {"post": {"security": [{"BearerAuth": []}], "description": "Memproses pembelian akun VPN per jam baru.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Purchase"], "summary": "Buy hourly accounts", "parameters": [{"description": "Data Pembelian Per Jam", "name": "purchase", "in": "body", "required": true, "schema": {"$ref": "#/definitions/purchase.PurchaseHourlyRequest"}}], "responses": {"201": {"description": "Created", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "402": {"description": "Saldo tidak men<PERSON>", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Server tidak di<PERSON>n", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "409": {"description": "Username sudah ada atau protokol tidak aktif", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/purchase/monthly": {"post": {"security": [{"BearerAuth": []}], "description": "Allows an authenticated user to purchase a new monthly VPN account.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Purchase"], "summary": "Purchase Monthly VPN Account", "parameters": [{"description": "Purchase Request", "name": "purchase_request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/purchase.PurchaseMonthlyRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.SuccessResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "409": {"description": "Conflict", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/purchase/trial": {"post": {"security": [{"BearerAuth": []}], "description": "Processes a new trial VPN account purchase. Limited to one per user. Username is auto-generated.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Purchase"], "summary": "Create a new trial account purchase record", "parameters": [{"description": "Trial Purchase Data", "name": "purchase", "in": "body", "required": true, "schema": {"$ref": "#/definitions/purchase.PurchaseTrialRequest"}}], "responses": {"201": {"description": "Created", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Jatah trial sudah habis", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Server tidak di<PERSON>n", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "409": {"description": "Layanan tidak tersedia atau tidak dapat membuat username unik", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/servers": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of all VPS servers", "produces": ["application/json"], "tags": ["server"], "summary": "Get all servers", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/server.PublicServerListResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new VPS server with detailed information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["server"], "summary": "Create a new server", "parameters": [{"description": "Server data", "name": "server", "in": "body", "required": true, "schema": {"$ref": "#/definitions/server.CreateServerRequest"}}], "responses": {"201": {"description": "Created", "schema": {"$ref": "#/definitions/server.SwaggerServerResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "409": {"description": "Kode already exists", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/servers/migrate": {"post": {"security": [{"BearerAuth": []}], "description": "Migrate data from legacy my_servers table to the new server model", "produces": ["application/json"], "tags": ["server"], "summary": "Migrate legacy server data", "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/servers/test-token": {"post": {"security": [{"BearerAuth": []}], "description": "Tests the connection to a server using provided domain and token without saving to database.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["server"], "summary": "Test new server token", "parameters": [{"description": "Domain and token to test", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/server.TestTokenRequest"}}], "responses": {"200": {"description": "Successful response from the server", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid request data", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal server error or failed to connect to the server", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/servers/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Get detailed information about a specific VPS server", "produces": ["application/json"], "tags": ["server"], "summary": "Get a server by ID", "parameters": [{"type": "integer", "description": "Server ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/server.PublicServerResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update information for an existing VPS server", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["server"], "summary": "Update a server", "parameters": [{"type": "integer", "description": "Server ID", "name": "id", "in": "path", "required": true}, {"description": "Server data to update", "name": "server", "in": "body", "required": true, "schema": {"$ref": "#/definitions/server.UpdateServerRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/server.SwaggerServerResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Soft delete a VPS server by ID", "produces": ["application/json"], "tags": ["server"], "summary": "Delete a server", "parameters": [{"type": "integer", "description": "Server ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/servers/{id}/test-token": {"get": {"security": [{"BearerAuth": []}], "description": "Tests the connection to a server by making a request to its admin API with the stored token.", "produces": ["application/json"], "tags": ["server"], "summary": "Test server token", "parameters": [{"type": "integer", "description": "Server ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "Successful response from the server", "schema": {"type": "object", "additionalProperties": true}}, "400": {"description": "Invalid ServerID", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Server not found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal server error or failed to connect to the server", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me": {"get": {"security": [{"BearerAuth": []}], "description": "Get the profile of the currently logged-in user", "produces": ["application/json"], "tags": ["User"], "summary": "Get current user's profile", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/vpn-shop_backend-go_handlers_user.UserResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update the profile of the currently logged-in user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "Update current user's profile", "parameters": [{"description": "User data to update", "name": "user", "in": "body", "required": true, "schema": {"$ref": "#/definitions/user.UpdateProfileRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/vpn-shop_backend-go_handlers_user.UserResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "409": {"description": "Email already in use", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/change-password": {"put": {"security": [{"BearerAuth": []}], "description": "Allows the authenticated user to change their password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["User"], "summary": "Change user's password", "parameters": [{"description": "Old and new passwords", "name": "passwords", "in": "body", "required": true, "schema": {"$ref": "#/definitions/user.ChangePasswordRequest"}}], "responses": {"200": {"description": "OK", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/invoices": {"get": {"security": [{"BearerAuth": []}], "description": "Get a list of all invoices for the currently logged-in user", "produces": ["application/json"], "tags": ["User"], "summary": "Get user's invoices", "parameters": [{"type": "integer", "default": 1, "description": "Page number for pagination", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Number of items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/user.PaginatedInvoicesResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/invoices/{invoice_id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieves detailed information for a specific invoice for the authenticated user.", "produces": ["application/json"], "tags": ["User"], "summary": "Get User Invoice Details", "parameters": [{"type": "string", "description": "Invoice ID", "name": "invoice_id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/payment.Transaction"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Invoice not found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal server error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/notifications": {"get": {"security": [{"BearerAuth": []}], "description": "Get paginated list of notifications for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Get user notifications", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/notification.NotificationListResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/notifications/mark-all-read": {"post": {"security": [{"BearerAuth": []}], "description": "Mark all notifications as read for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Mark all notifications as read", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.SuccessResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/notifications/mark-read": {"post": {"security": [{"BearerAuth": []}], "description": "Mark specific notifications as read for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Mark notifications as read", "parameters": [{"description": "Notification IDs to mark as read", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/notification.MarkAsReadRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.SuccessResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/notifications/stats": {"get": {"security": [{"BearerAuth": []}], "description": "Get notification statistics (total and unread count) for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Get notification statistics", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/notification.NotificationStatsResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/notifications/{id}": {"delete": {"security": [{"BearerAuth": []}], "description": "Delete a specific notification for the authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["Notifications"], "summary": "Delete notification", "parameters": [{"type": "integer", "description": "Notification ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/shared.SuccessResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/photo": {"post": {"security": [{"BearerAuth": []}], "description": "Upload a profile photo for the currently logged-in user", "consumes": ["multipart/form-data"], "produces": ["application/json"], "tags": ["User"], "summary": "Upload user profile photo", "parameters": [{"type": "file", "description": "Photo file to upload", "name": "photo", "in": "formData", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/user.UploadPhotoResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "413": {"description": "File too large", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "415": {"description": "Unsupported file type", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/stats": {"get": {"security": [{"BearerAuth": []}], "description": "Get aggregated statistics like account counts for the currently logged-in user", "produces": ["application/json"], "tags": ["User"], "summary": "Get current user's statistics", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/user.UserStatsResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/transactions": {"get": {"security": [{"BearerAuth": []}], "description": "Mengambil daftar transaksi pengguna yang sedang login dengan paginasi dan filter.", "produces": ["application/json"], "tags": ["User"], "summary": "Get User Transactions", "parameters": [{"type": "integer", "default": 1, "description": "<PERSON><PERSON>", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Jumlah item per halaman", "name": "limit", "in": "query"}, {"type": "string", "description": "<PERSON><PERSON> kunci pen<PERSON>ian", "name": "search", "in": "query"}, {"type": "string", "description": "Filter berdasarkan tipe transaksi (TOP<PERSON>, PURCHASE_MONTHLY, PURCHASE_HOURLY, TRIAL, RENEWAL, REFUND, BILLING, BILLED_HOURLY)", "name": "type", "in": "query"}, {"type": "string", "description": "Filter berdasarkan status transaksi (PENDING, SUCCESS, FAILED)", "name": "status", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/user.TransactionListResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}, "/users/me/transactions/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Mengambil detail transaksi pengguna berdasarkan ID.", "produces": ["application/json"], "tags": ["User"], "summary": "Get User Transaction By ID", "parameters": [{"type": "integer", "description": "Transaction ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/user.TransactionResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/shared.ErrorResponse"}}}}}}, "definitions": {"account.RenewRequest": {"type": "object", "required": ["account_id", "account_type", "duration_months", "metode", "p<PERSON><PERSON><PERSON>"], "properties": {"account_id": {"type": "integer"}, "account_type": {"type": "string", "enum": ["trojan", "vmess", "vless", "ssh"]}, "duration_months": {"type": "integer", "minimum": 1}, "metode": {"type": "string"}, "pembayaran": {"type": "string", "enum": ["SALDO", "TRIPAY"]}}}, "admin.AdminSetting": {"type": "object", "properties": {"daily_mt_time": {"description": "Waktu maintenance harian dalam format \"HH:MM\".", "type": "string"}, "expired_minutes": {"description": "<PERSON><PERSON><PERSON> dalam menit sebelum akun dianggap kadalu<PERSON>.", "type": "integer"}, "hourly_billing_interval": {"description": "Interval penagihan per jam dalam menit.", "type": "integer"}, "min_saldo": {"description": "Saldo minimal yang harus dimiliki pengguna.", "type": "integer"}, "min_top_up": {"description": "Nominal minimal untuk sekali top-up member.", "type": "integer"}, "min_top_up_reseller": {"description": "Nominal minimal untuk sekali top-up reseller.", "type": "integer"}}}, "admin.CreateAnnouncementRequest": {"type": "object", "required": ["isi", "judul"], "properties": {"isi": {"type": "string"}, "judul": {"type": "string"}}}, "admin.RoleResponse": {"type": "object", "properties": {"name": {"type": "string"}}}, "admin.SwaggerUserDetailResponse": {"type": "object", "properties": {"account_type": {"type": "string"}, "created_at": {"type": "string"}, "email": {"type": "string"}, "email_verified": {"type": "boolean"}, "id": {"type": "integer"}, "jumlah_akun_hourly": {"type": "integer"}, "jumlah_akun_month": {"type": "integer"}, "jumlah_akun_ssh": {"type": "integer"}, "jumlah_akun_trial": {"type": "integer"}, "jumlah_akun_trojan": {"type": "integer"}, "jumlah_akun_vless": {"type": "integer"}, "jumlah_akun_vmess": {"type": "integer"}, "name": {"type": "string"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/admin.RoleResponse"}}, "saldo": {"type": "integer"}, "suspend": {"type": "string"}, "total_account": {"type": "integer"}, "username": {"type": "string"}, "verif_wa": {"type": "boolean"}, "whatsapp": {"type": "string"}}}, "admin.SwaggerUserListResponse": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/definitions/admin.SwaggerUserDetailResponse"}}}}, "admin.UpdateAnnouncementRequest": {"type": "object", "required": ["isi", "judul"], "properties": {"isi": {"type": "string"}, "judul": {"type": "string"}}}, "admin.UpdateUserRequest": {"type": "object", "properties": {"email": {"type": "string"}, "name": {"type": "string"}, "roles": {"description": "Admin can update roles", "type": "array", "items": {"type": "string"}}, "saldo": {"type": "integer"}, "whatsapp": {"type": "string"}}}, "announcement.AnnouncementResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "id": {"type": "integer"}, "isi": {"type": "string"}, "judul": {"type": "string"}, "updated_at": {"type": "string"}}}, "auth.RegisterRequest": {"type": "object", "required": ["email", "name", "password", "username", "whatsapp"], "properties": {"email": {"type": "string", "example": "<EMAIL>"}, "name": {"type": "string", "example": "<PERSON>"}, "password": {"type": "string", "minLength": 6, "example": "password123"}, "username": {"type": "string", "example": "johndoe"}, "whatsapp": {"type": "string", "minLength": 10, "example": "************"}}}, "auth.TelegramAuthRequest": {"description": "Create a new user account", "type": "object", "required": ["auth_date", "hash", "id"], "properties": {"auth_date": {"type": "integer"}, "first_name": {"type": "string"}, "hash": {"type": "string"}, "id": {"type": "integer"}, "last_name": {"type": "string"}, "photo_url": {"type": "string"}, "username": {"type": "string"}}}, "notification.MarkAsReadRequest": {"type": "object", "properties": {"notification_ids": {"type": "array", "items": {"type": "integer"}}}}, "notification.NotificationData": {"type": "object", "properties": {"account_id": {"type": "integer"}, "account_type": {"type": "string"}, "amount": {"type": "integer"}, "duration": {"type": "string"}, "invoice_id": {"type": "string"}, "payment_method": {"type": "string"}, "server_code": {"type": "string"}, "username": {"type": "string"}}}, "notification.NotificationListResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "notifications": {"type": "array", "items": {"$ref": "#/definitions/notification.NotificationResponse"}}, "page": {"type": "integer"}, "total": {"type": "integer"}, "unread_count": {"type": "integer"}}}, "notification.NotificationResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "data": {"$ref": "#/definitions/notification.NotificationData"}, "id": {"type": "integer"}, "is_read": {"type": "boolean"}, "message": {"type": "string"}, "read_at": {"type": "string"}, "title": {"type": "string"}, "type": {"$ref": "#/definitions/notification.NotificationType"}}}, "notification.NotificationStatsResponse": {"type": "object", "properties": {"total_count": {"type": "integer"}, "unread_count": {"type": "integer"}}}, "notification.NotificationType": {"type": "string", "enum": ["trial", "monthly", "hourly", "renewal", "topup", "payment", "billing"], "x-enum-varnames": ["NotificationTypeTrial", "NotificationTypeMonthly", "NotificationTypeHourly", "NotificationTypeRenewal", "NotificationTypeTopup", "NotificationTypePayment", "NotificationTypeBilling"]}, "payment.CreatePaymentRequest": {"type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/definitions/payment.PurchaseItem"}}, "payment_method": {"type": "string"}}}, "payment.Fee": {"type": "object", "properties": {"flat": {"type": "integer"}, "percent": {"type": "number"}}}, "payment.PaymentChannel": {"type": "object", "properties": {"active": {"type": "boolean"}, "code": {"type": "string"}, "fee_customer": {"$ref": "#/definitions/payment.Fee"}, "fee_merchant": {"$ref": "#/definitions/payment.Fee"}, "group": {"type": "string"}, "icon_url": {"type": "string"}, "maximum_amount": {"type": "integer"}, "maximum_fee": {"type": "integer"}, "minimum_amount": {"type": "integer"}, "minimum_fee": {"type": "integer"}, "name": {"type": "string"}, "total_fee": {"$ref": "#/definitions/payment.TotalFee"}, "type": {"type": "string"}}}, "payment.PurchaseItem": {"type": "object", "properties": {"quantity": {"type": "integer"}, "server_id": {"type": "integer"}}}, "payment.TopUpRequest": {"type": "object", "properties": {"amount": {"type": "integer"}, "payment_method": {"type": "string"}}}, "payment.TotalFee": {"type": "object", "properties": {"flat": {"type": "integer"}, "percent": {"type": "string"}}}, "payment.Transaction": {"type": "object", "properties": {"account_id": {"type": "integer"}, "account_type": {"type": "string"}, "amount": {"type": "integer"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "duration": {"type": "string"}, "gateway_checkout_url": {"type": "string"}, "gateway_reference": {"type": "string"}, "id": {"type": "integer"}, "invoice_id": {"type": "string"}, "payment_gateway": {"type": "string"}, "status": {"$ref": "#/definitions/payment.TransactionStatus"}, "type": {"$ref": "#/definitions/payment.TransactionType"}, "updated_at": {"type": "string"}, "user": {"$ref": "#/definitions/user.User"}, "user_id": {"type": "integer"}}}, "payment.TransactionStatus": {"type": "string", "enum": ["PENDING", "SUCCESS", "FAILED", "EXPIRED"], "x-enum-varnames": ["Pending", "Success", "Failed", "Expired"]}, "payment.TransactionType": {"type": "string", "enum": ["TOPUP", "PURCHASE_MONTHLY", "PURCHASE_HOURLY", "TRIAL", "RENEWAL", "REFUND", "BILLING", "BILLED_HOURLY"], "x-enum-varnames": ["Topup", "PurchaseMonthly", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Trial", "Renewal", "Refund", "Billing", "<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "payment.TripayChannelsResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/payment.PaymentChannel"}}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "payment.TripayTransactionStatus": {"type": "object", "properties": {"data": {"type": "object", "properties": {"amount": {"type": "integer"}, "amount_received": {"type": "integer"}, "callback_url": {"type": "string"}, "customer_email": {"type": "string"}, "customer_name": {"type": "string"}, "customer_phone": {"type": "string"}, "fee": {"type": "integer"}, "merchant_ref": {"type": "string"}, "note": {"type": "string"}, "paid_at": {"type": "integer"}, "payment_method": {"type": "string"}, "payment_method_name": {"type": "string"}, "reference": {"type": "string"}, "return_url": {"type": "string"}, "status": {"type": "string"}, "total_fee": {"type": "integer"}}}, "message": {"type": "string"}, "success": {"type": "boolean"}}}, "public.TransactionResponse": {"type": "object", "properties": {"amount": {"type": "integer"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "invoice_id": {"type": "string"}, "user_avatar": {"type": "string"}, "user_name": {"type": "string"}}}, "purchase.PurchaseHourlyRequest": {"type": "object", "required": ["kode_server", "protocol", "username"], "properties": {"kode_server": {"type": "string"}, "protocol": {"type": "string", "enum": ["trojan", "vmess", "vless"]}, "username": {"type": "string"}}}, "purchase.PurchaseMonthlyRequest": {"type": "object", "required": ["bulan", "kode_server", "metode", "p<PERSON><PERSON><PERSON>", "protocol", "username"], "properties": {"bulan": {"type": "integer", "maximum": 12, "minimum": 1}, "kode_server": {"type": "string"}, "metode": {"type": "string"}, "pembayaran": {"type": "string", "enum": ["SALDO", "TRIPAY"]}, "protocol": {"type": "string", "enum": ["trojan", "vmess", "vless"]}, "username": {"type": "string", "maxLength": 12, "minLength": 6}}}, "purchase.PurchaseTrialRequest": {"type": "object", "required": ["kode_server", "protocol"], "properties": {"kode_server": {"type": "string"}, "protocol": {"type": "string", "enum": ["trojan", "vmess", "vless"]}}}, "server.CreateServerRequest": {"type": "object", "required": ["domain", "harga_member", "harga_reseller", "max_device", "nama", "nama_isp", "negara", "slot_server", "token"], "properties": {"domain": {"type": "string"}, "harga_member": {"type": "integer", "minimum": 0}, "harga_reseller": {"type": "integer", "minimum": 0}, "kode": {"description": "Made optional - will be auto-generated if empty", "type": "string"}, "max_device": {"type": "integer", "minimum": 1}, "nama": {"type": "string"}, "nama_isp": {"type": "string"}, "negara": {"type": "string"}, "slot_server": {"type": "integer", "minimum": 1}, "slot_terpakai": {"type": "integer"}, "ssh": {"type": "string"}, "token": {"type": "string"}, "total_user": {"type": "integer"}, "trojan": {"type": "string"}, "vless": {"type": "string"}, "vmess": {"type": "string"}}}, "server.PublicServerListResponse": {"type": "object", "properties": {"servers": {"type": "array", "items": {"$ref": "#/definitions/server.PublicServerResponse"}}}}, "server.PublicServerResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "domain": {"type": "string"}, "harga_member": {"type": "integer"}, "harga_reseller": {"type": "integer"}, "kode": {"type": "string"}, "max_device": {"type": "integer"}, "nama": {"type": "string"}, "nama_isp": {"type": "string"}, "negara": {"type": "string"}, "server_id": {"type": "integer"}, "slot_server": {"type": "integer"}, "slot_terpakai": {"type": "integer"}, "ssh": {"type": "string"}, "total_user": {"type": "integer"}, "trojan": {"type": "string"}, "updated_at": {"type": "string"}, "vless": {"type": "string"}, "vmess": {"type": "string"}}}, "server.SwaggerServerResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "domain": {"type": "string"}, "harga_member": {"type": "integer"}, "harga_reseller": {"type": "integer"}, "kode": {"type": "string"}, "max_device": {"type": "integer"}, "nama": {"type": "string"}, "nama_isp": {"type": "string"}, "negara": {"type": "string"}, "server_id": {"type": "integer"}, "slot_server": {"type": "integer"}, "slot_terpakai": {"type": "integer"}, "ssh": {"type": "string"}, "token": {"type": "string"}, "total_user": {"type": "integer"}, "trojan": {"type": "string"}, "updated_at": {"type": "string"}, "vless": {"type": "string"}, "vmess": {"type": "string"}}}, "server.TestTokenRequest": {"type": "object", "required": ["api_path", "domain", "token"], "properties": {"api_path": {"type": "string"}, "domain": {"type": "string"}, "token": {"type": "string"}}}, "server.UpdateServerRequest": {"type": "object", "properties": {"domain": {"type": "string"}, "harga_member": {"type": "integer", "minimum": 0}, "harga_reseller": {"type": "integer", "minimum": 0}, "max_device": {"type": "integer", "minimum": 1}, "nama": {"type": "string"}, "nama_isp": {"type": "string"}, "negara": {"type": "string"}, "slot_server": {"type": "integer", "minimum": 1}, "slot_terpakai": {"type": "integer"}, "ssh": {"type": "string"}, "token": {"type": "string"}, "total_user": {"type": "integer"}, "trojan": {"type": "string"}, "vless": {"type": "string"}, "vmess": {"type": "string"}}}, "shared.AccountDetailResponse": {"type": "object", "properties": {"account_type": {"type": "string"}, "connection_links": {"type": "array", "items": {"$ref": "#/definitions/shared.ProcessedLink"}}, "data_limit_gb": {"type": "number"}, "expired_date": {"type": "string"}, "protocol": {"type": "string"}, "server": {"$ref": "#/definitions/shared.ServerInfoForAccountDetail"}, "status": {"type": "string"}, "subscription_url": {"type": "string"}, "used_traffic_gb": {"type": "number"}, "username": {"type": "string"}, "uuid": {"type": "string"}}}, "shared.ErrorResponse": {"type": "object", "properties": {"details": {"description": "Menggunakan interface{} agar bisa menampung berbagai jenis detail error"}, "error": {"type": "string"}}}, "shared.Pagination": {"type": "object", "properties": {"current_page": {"type": "integer", "example": 1}, "last_page": {"type": "integer", "example": 10}, "per_page": {"type": "integer", "example": 10}, "total": {"type": "integer", "example": 100}}}, "shared.ProcessedLink": {"type": "object", "properties": {"name": {"type": "string"}, "url": {"type": "string"}}}, "shared.RenewSuccessResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "Account renewed successfully"}, "new_balance": {"type": "integer", "example": 50000}, "new_expire": {"type": "string", "example": "2024-12-31"}}}, "shared.ServerInfoForAccountDetail": {"type": "object", "properties": {"created_at": {"type": "string"}, "domain": {"type": "string"}, "harga_member": {"type": "integer"}, "harga_reseller": {"type": "integer"}, "kode": {"type": "string"}, "nama": {"type": "string"}, "nama_isp": {"type": "string"}, "negara": {"type": "string"}, "server_id": {"type": "integer"}, "ssh": {"type": "string"}, "trojan": {"type": "string"}, "updated_at": {"type": "string"}, "vless": {"type": "string"}, "vmess": {"type": "string"}}}, "shared.SuccessResponse": {"type": "object", "properties": {"message": {"type": "string"}, "success": {"type": "boolean"}}}, "shared.TopUpResponse": {"type": "object", "properties": {"checkout_url": {"type": "string", "example": "https://tripay.co.id/checkout/INV12345"}}}, "stats.TopServerResponse": {"type": "object", "properties": {"server_country": {"type": "string"}, "server_kode": {"type": "string"}, "server_name": {"type": "string"}, "slot_server": {"type": "integer"}, "slot_terpakai": {"type": "integer"}, "total_user": {"type": "integer"}}}, "user.ChangePasswordRequest": {"type": "object", "required": ["new_password", "old_password"], "properties": {"new_password": {"type": "string", "minLength": 6}, "old_password": {"type": "string"}}}, "user.PaginatedInvoicesResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/payment.Transaction"}}, "limit": {"type": "integer"}, "page": {"type": "integer"}, "total": {"type": "integer"}}}, "user.Role": {"type": "object", "properties": {"createdAt": {"type": "string"}, "deletedAt": {"type": "string", "format": "date-time"}, "description": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "updatedAt": {"type": "string"}, "users": {"type": "array", "items": {"$ref": "#/definitions/user.User"}}}}, "user.ServiceHistoryItem": {"type": "object", "properties": {"expired": {"description": "Fields for invoice page (when clicking \"Detail\")", "type": "string"}, "kode_akun": {"type": "string"}, "kode_server": {"description": "Server code for navigation to detail page", "type": "string"}, "layanan": {"description": "The server domain", "type": "string"}, "nama_isp": {"description": "The ISP name", "type": "string"}, "nama_server": {"description": "The server name", "type": "string"}, "order_id": {"description": "Unique identifier for the order/account", "type": "string"}, "service_type": {"description": "\"trojan\", \"vmess\", \"vless\", \"ssh\"", "type": "string"}, "status": {"type": "string"}, "tanggal_beli": {"description": "Fields for table display", "type": "string"}, "tipe": {"description": "e.g., \"trojan-monthly\"", "type": "string"}, "username": {"description": "The account username (e.g., trial-xyz)", "type": "string"}}}, "user.ServiceHistoryResponse": {"type": "object", "properties": {"history": {"type": "array", "items": {"$ref": "#/definitions/user.ServiceHistoryItem"}}, "limit": {"type": "integer"}, "page": {"type": "integer"}, "total": {"type": "integer"}}}, "user.SwaggerRoleResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "updated_at": {"type": "string"}}}, "user.TransactionListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/user.TransactionResponse"}}, "pagination": {"$ref": "#/definitions/shared.Pagination"}}}, "user.TransactionResponse": {"type": "object", "properties": {"amount": {"type": "integer"}, "created_at": {"type": "string"}, "description": {"type": "string"}, "id": {"type": "integer"}, "invoice_id": {"type": "string"}, "status": {"type": "string"}, "type": {"type": "string"}, "user_avatar": {"type": "string"}, "user_name": {"type": "string"}}}, "user.UpdateProfileRequest": {"type": "object", "properties": {"email": {"type": "string"}, "name": {"type": "string"}, "whatsapp": {"type": "string"}}}, "user.UploadPhotoResponse": {"type": "object", "properties": {"message": {"type": "string"}, "photo_url": {"type": "string"}}}, "user.User": {"type": "object", "properties": {"batasTrial": {"type": "integer"}, "createdAt": {"type": "string"}, "deleteHourlyCount": {"type": "integer"}, "deletedAt": {"type": "string", "format": "date-time"}, "email": {"type": "string"}, "emailVerified": {"type": "integer"}, "emailVerifiedAt": {"type": "string"}, "id": {"type": "integer"}, "laba": {"type": "integer"}, "lastDeleteHourlyAt": {"type": "string"}, "lastLogin": {"type": "string"}, "limitDeleteHourly": {"type": "integer"}, "name": {"type": "string"}, "notifLogin": {"type": "integer"}, "otp": {"type": "integer"}, "pathPhoto": {"type": "string"}, "payBulanan": {"type": "integer"}, "payHarian": {"type": "integer"}, "payMingguan": {"type": "integer"}, "payPerjam": {"type": "integer"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/user.Role"}}, "saldo": {"type": "integer"}, "suspend": {"type": "string"}, "telegram": {"type": "string"}, "totalPay": {"type": "integer"}, "trial": {"type": "integer"}, "updatedAt": {"type": "string"}, "userTelegram": {"type": "string"}, "username": {"type": "string"}, "verifWa": {"type": "integer"}, "whatsapp": {"type": "string"}}}, "user.UserStatsResponse": {"type": "object", "properties": {"batas_trial": {"type": "integer"}, "jumlah_akun_hourly": {"type": "integer"}, "jumlah_akun_month": {"type": "integer"}, "jumlah_akun_ssh": {"type": "integer"}, "jumlah_akun_trial": {"type": "integer"}, "jumlah_akun_trojan": {"type": "integer"}, "jumlah_akun_vless": {"type": "integer"}, "jumlah_akun_vmess": {"type": "integer"}, "pay_bulanan": {"type": "integer"}, "total_account": {"type": "integer"}, "total_pay": {"type": "integer"}, "trial": {"type": "integer"}}}, "vpn-shop_backend-go_handlers_user.UserResponse": {"type": "object", "properties": {"account_type": {"type": "string"}, "email": {"type": "string"}, "email_verified": {"type": "boolean"}, "email_verified_at": {"type": "string"}, "id": {"type": "integer"}, "name": {"type": "string"}, "photo_url": {"type": "string"}, "roles": {"type": "array", "items": {"$ref": "#/definitions/user.SwaggerRoleResponse"}}, "saldo": {"type": "integer"}, "username": {"type": "string"}, "whatsapp": {"type": "string"}, "whatsapp_verified": {"type": "boolean"}}}}, "securityDefinitions": {"BearerAuth": {"description": "\"Type 'Bearer ' followed by your JWT token to authorize.\"", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}