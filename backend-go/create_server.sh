#!/bin/bash

# CATATAN: Pastikan server telah dijalankan terlebih dahulu dengan perintah:
# cd backend-go && go run main.go

# URL endpoint untuk backend Go
LOGIN_URL="http://localhost:8000/api/v1/auth/login"
SERVER_URL="http://localhost:8000/api/v1/servers"
HEADERS="Content-Type: application/json"

echo "Script untuk testing pembuatan server VPS..."
echo "PASTIKAN SERVER SUDAH BERJALAN DENGAN: cd backend-go && go run main.go"

# Login untuk mendapatkan token
echo "Login sebagai admin untuk mendapatkan token..."
LOGIN_DATA=$(cat <<EOF
{
    "identifier": "admin",
    "password": "admin"
}
EOF
)

login_response=$(curl -s -w "\n%{http_code}" -X POST "$LOGIN_URL" \
     -H "$HEADERS" \
     -d "$LOGIN_DATA")

login_http_code=$(tail -n1 <<< "$login_response")
login_body=$(sed '$ d' <<< "$login_response")

if [ "$login_http_code" -ne 200 ]; then
    echo "  -> Login gagal! Status: $login_http_code, Detail: $login_body"
    echo "     Pastikan server berjalan dan user admin ada."
    exit 1
fi

# Ekstrak token dari response
TOKEN=$(echo $login_body | grep -o '"accessToken":"[^"]*' | cut -d'"' -f4)

if [ -z "$TOKEN" ]; then
    echo "  -> Gagal mendapatkan token dari response."
    exit 1
fi

echo "  -> Login berhasil! Token diperoleh."

# Jumlah server yang akan dibuat, bisa dari argumen command line
# Gunakan: ./create_server.sh 5  (untuk membuat 5 server)
NUM_SERVERS=${1:-3} # Default 3 jika tidak ada argumen
echo -e "\nAkan membuat $NUM_SERVERS server baru..."

for i in $(seq 1 $NUM_SERVERS)
do
    # Membuat data unik dan acak untuk setiap server
    RANDOM_SUFFIX=$(cat /dev/urandom | tr -dc 'a-z0-9' | fold -w 6 | head -n 1)
    RANDOM_NUM=$((RANDOM % 900 + 100)) # Angka acak 3 digit

    NAMA="Server-$RANDOM_SUFFIX"
    KODE="SVR-$RANDOM_NUM"
    DOMAIN="$RANDOM_SUFFIX.io"
    TOKEN_SERVER="token-$RANDOM_SUFFIX"
    NEGARA="indonesia"
    ISP="Digital Ocean"
    # Mengatur status layanan sesuai permintaan
    SSH="disabled"
    TROJAN="enabled"
    VMESS="enabled"
    VLESS="enabled"


    echo "=> Membuat server ke-$i: $NAMA ($KODE)"

    SERVER_DATA=$(cat <<EOF
{
    "nama": "$NAMA",
    "kode": "$KODE",
    "domain": "$DOMAIN",
    "token": "$TOKEN_SERVER",
    "negara": "$NEGARA",
    "nama_isp": "$ISP",
    "harga_member": 50000,
    "harga_reseller": 35000,
    "ssh": "$SSH",
    "trojan": "$TROJAN",
    "vmess": "$VMESS",
    "vless": "$VLESS",
    "slot_server": 100,
    "slot_terpakai": 50,
    "total_user": 50,
    "max_device": 2
}
EOF
)

    # Mengirim permintaan POST untuk membuat server
    response=$(curl -s -w "\n%{http_code}" -X POST "$SERVER_URL" \
         -H "$HEADERS" \
         -H "Authorization: Bearer $TOKEN" \
         -d "$SERVER_DATA")

    # Ekstrak HTTP status code dan body
    http_code=$(tail -n1 <<< "$response")
    body=$(sed '$ d' <<< "$response")

    # Memeriksa kode status 201 (Created)
    if [ "$http_code" -eq 201 ]; then
        echo "  -> Berhasil! Server '$NAMA' dibuat. Status: $http_code"
    else
        echo "  -> GAGAL membuat server '$NAMA'! Status: $http_code, Detail: $body"
    fi
done

# Mengambil daftar server untuk memverifikasi setelah semua dibuat
echo -e "\nProses pembuatan selesai. Mengambil daftar server untuk verifikasi..."
list_response=$(curl -s -w "\n%{http_code}" -X GET "$SERVER_URL" \
     -H "Authorization: Bearer $TOKEN")

list_http_code=$(tail -n1 <<< "$list_response")
list_body=$(sed '$ d' <<< "$list_response")

if [ "$list_http_code" -eq 200 ]; then
    echo "  -> Berhasil mendapatkan daftar server. Status: $list_http_code"
    echo "     Jumlah server sekarang: $(echo $list_body | jq '.servers | length')"
    # echo "     Respons: $list_body" # Uncomment jika ingin lihat detail
else
    echo "  -> Gagal mendapatkan daftar server! Status: $list_http_code, Detail: $list_body"
fi

echo -e "\nProses testing selesai."