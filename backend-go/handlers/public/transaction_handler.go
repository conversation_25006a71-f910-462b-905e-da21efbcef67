package public

import (
	"net/http"
	"strings"
	"time"

	"vpn-shop/backend-go/db"
	models "vpn-shop/backend-go/models/payment"
	"vpn-shop/backend-go/models/shared"

	"github.com/labstack/echo/v4"
)

// TransactionResponse adalah DTO untuk menampilkan data transaksi publik.
type TransactionResponse struct {
	ID          uint      `json:"id"`
	InvoiceID   string    `json:"invoice_id"`
	UserName    string    `json:"user_name"`
	UserAvatar  *string   `json:"user_avatar,omitempty"`
	Description string    `json:"description"`
	Amount      int64     `json:"amount"`
	CreatedAt   time.Time `json:"created_at"`
}

// GetPublicTransactions mengambil daftar transaksi publik berdasarkan tipe.
// @Summary Get Public Transactions
// @Description Mengambil daftar transaksi publik. Tipe bisa 'topup', 'purchase', atau 'trial'.
// @Tags Public
// @Produce  json
// @Param    type   query   string  true  "Transaction Type" Enums(topup,purchase,trial)
// @Success 200 {array} TransactionResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /public/transactions [get]
func GetPublicTransactions(c echo.Context) error {
	txType := strings.ToUpper(c.QueryParam("type"))

	var conditions []models.TransactionType
	switch txType {
	case "TOPUP":
		conditions = append(conditions, models.Topup)
	case "PURCHASE":
		conditions = append(conditions, models.PurchaseMonthly, models.PurchaseHourly)
	case "TRIAL":
		conditions = append(conditions, models.Trial)
	default:
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Tipe transaksi tidak valid. Gunakan 'topup', 'purchase', atau 'trial'."})
	}

	var transactions []models.Transaction
	query := db.DB.Preload("User").
		Where("type IN ?", conditions).
		Where("status = ?", models.Success).
		Order("created_at desc").
		Limit(10).
		Find(&transactions)

	if query.Error != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data transaksi"})
	}

	responses := make([]TransactionResponse, len(transactions))
	for i, t := range transactions {
		responses[i] = TransactionResponse{
			ID:          t.ID,
			InvoiceID:   t.InvoiceID,
			UserName:    t.User.Name,
			UserAvatar:  t.User.PathPhoto,
			Description: t.Description,
			Amount:      t.Amount,
			CreatedAt:   t.CreatedAt,
		}
	}

	return c.JSON(http.StatusOK, responses)
}
