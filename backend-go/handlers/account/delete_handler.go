package account

import (
	"fmt"
	"log"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/middleware"
	"vpn-shop/backend-go/models/account"
	"vpn-shop/backend-go/models/payment"
	"vpn-shop/backend-go/models/server"
	"vpn-shop/backend-go/models/shared"
	"vpn-shop/backend-go/models/user"
	"vpn-shop/backend-go/tasks"
	"vpn-shop/backend-go/utils"

	"github.com/golang-jwt/jwt/v5"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// DeleteAccount godoc
// @Summary      Delete a user account
// @Description  Deletes a user's VPN account. <PERSON><PERSON> can delete any account, while regular users have an hourly limit. For hourly accounts, performs a final billing check.
// @Tags         Accounts
// @Accept       json
// @Produce      json
// @Param        account_id   path      int     true  "Account ID"
// @Param        account_type query     string  true  "Account Type" Enums(trojan, vmess, vless, ssh)
// @Success      200          {object}  shared.SuccessResponse
// @Failure      400          {object}  shared.ErrorResponse
// @Failure      401          {object}  shared.ErrorResponse
// @Failure      403          {object}  shared.ErrorResponse
// @Failure      404          {object}  shared.ErrorResponse
// @Failure      500          {object}  shared.ErrorResponse
// @Security     OAuth2Password
// @Router       /accounts/{account_id} [delete]
func DeleteAccount(c echo.Context) error {
	// --- 1. Get User Info from JWT ---
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	// Get role from JWT token
	userToken, ok := c.Get("user").(*jwt.Token)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Token JWT tidak valid atau tidak ditemukan"})
	}
	claims, ok := userToken.Claims.(*jwt.MapClaims)
	if !ok {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Format klaim JWT tidak valid"})
	}
	role, ok := (*claims)["role"].(string)
	if !ok {
		role = "user" // Default to "user" if role is not present
	}

	// --- 2. Parse Input ---
	accountIDStr := c.Param("account_id")
	accountID, err := strconv.ParseUint(accountIDStr, 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format ID akun tidak valid"})
	}

	accountType := c.QueryParam("account_type")
	if accountType == "" {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Parameter query account_type diperlukan"})
	}

	// --- 3. Database Transaction ---
	tx := db.DB.Begin()
	if tx.Error != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memulai transaksi database"})
	}
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// --- 4. Find Account & Authorize ---
	var genericAcc GenericAccount
	var accountToDelete interface{}

	handleError := func(err error) error {
		tx.Rollback()
		if err == gorm.ErrRecordNotFound {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Akun tidak ditemukan"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan database saat mencari akun"})
	}

	switch accountType {
	case "trojan":
		var acc account.AccountTrojan
		err = tx.First(&acc, accountID).Error
		if err != nil {
			return handleError(err)
		}
		genericAcc = toGenericAccount(&acc)
		accountToDelete = &acc
	case "vmess":
		var acc account.AccountVmess
		err = tx.First(&acc, accountID).Error
		if err != nil {
			return handleError(err)
		}
		genericAcc = toGenericAccount(&acc)
		accountToDelete = &acc
	case "vless":
		var acc account.AccountVless
		err = tx.First(&acc, accountID).Error
		if err != nil {
			return handleError(err)
		}
		genericAcc = toGenericAccount(&acc)
		accountToDelete = &acc
	case "ssh":
		var acc account.AccountSsh
		err = tx.First(&acc, accountID).Error
		if err != nil {
			return handleError(err)
		}
		genericAcc = toGenericAccount(&acc)
		accountToDelete = &acc
	default:
		tx.Rollback()
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Tipe akun yang ditentukan tidak valid"})
	}

	if role != "admin" && genericAcc.UserID != fmt.Sprintf("%d", userID) {
		tx.Rollback()
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Anda tidak memiliki izin untuk menghapus akun ini"})
	}

	// --- 5. Final Billing Check for Hourly Accounts ---
	if genericAcc.IsHourly {
		now := time.Now()
		totalUnbilledMinutes := genericAcc.UnbilledMinutes

		if genericAcc.LastStart != nil {
			durationSinceLastStart := now.Sub(*genericAcc.LastStart)
			minutesSinceLastStart := int(math.Round(durationSinceLastStart.Minutes()))
			totalUnbilledMinutes += minutesSinceLastStart
		}

		if totalUnbilledMinutes >= 30 {
			hoursToBill := int(math.Ceil(float64(totalUnbilledMinutes) / 60.0)) // Round up to the nearest full hour
			log.Printf("INFO: Melakukan penagihan akhir untuk akun %d. Menit belum ditagih (%d) >= 30.", genericAcc.ID, totalUnbilledMinutes)

			var user user.User
			err = tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&user, "id = ?", genericAcc.UserID).Error
			if err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengunci pengguna untuk penagihan akhir"})
			}

			var harga int64
			harga, err = strconv.ParseInt(genericAcc.Harga, 10, 64)
			if err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Format harga tidak valid untuk penagihan akhir"})
			}

			totalCost := harga * int64(hoursToBill)

			if user.Saldo < totalCost {
				tx.Rollback()
				return c.JSON(http.StatusForbidden, shared.ErrorResponse{
					Error: fmt.Sprintf("Penghapusan gagal. Anda memiliki pemakaian %d menit yang belum dibayar. Silakan top up saldo Anda sebesar Rp %d sebelum menghapus akun ini.", totalUnbilledMinutes, totalCost),
				})
			}

			// Proceed with billing
			initialBalance := user.Saldo // Capture balance before update
			newBalance := initialBalance - totalCost

			err = tx.Model(&user).Update("saldo", newBalance).Error
			if err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui saldo pengguna selama penagihan akhir"})
			}

			history := payment.BillingHourlyHistory{
				UserID:           genericAcc.UserID,
				AccountID:        genericAcc.ID,
				AccountType:      accountType,
				BilledAt:         now,
				HoursBilled:      hoursToBill,
				AmountBilled:     totalCost,
				InitialBalance:   initialBalance, // Use captured value
				RemainingBalance: newBalance,
			}

			err = tx.Create(&history).Error
			if err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat riwayat penagihan akhir"})
			}

			// Update the account record before it's soft-deleted
			updateData := map[string]interface{}{
				"last_billed_at":   now,
				"total_hours":      gorm.Expr("total_hours + ?", hoursToBill),
				"unbilled_minutes": 0, // Reset celengan
				"last_start":       nil,
			}
			err = tx.Model(accountToDelete).Updates(updateData).Error
			if err != nil {
				tx.Rollback()
				return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui data penagihan akhir akun"})
			}

			// Log to CSV
			go tasks.LogBillingToCSV(user, toTaskGenericAccount(genericAcc, accountType), history)
			log.Printf("INFO: Melakukan penagihan akhir untuk akun %s (user %s) sebelum penghapusan.", genericAcc.Username, user.Name)
		}
	}

	// --- 6. Check User's Delete Limit (if not admin) ---
	if role != "admin" {
		var user user.User
		err = tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&user, userID).Error
		if err != nil {
			tx.Rollback()
			if err == gorm.ErrRecordNotFound {
				return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
			}
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan database saat mengambil data pengguna"})
		}

		now := time.Now()
		// Reset hourly count if the hour has passed
		if user.LastDeleteHourlyAt != nil && (now.Year() != user.LastDeleteHourlyAt.Year() || now.Month() != user.LastDeleteHourlyAt.Month() || now.Day() != user.LastDeleteHourlyAt.Day() || now.Hour() != user.LastDeleteHourlyAt.Hour()) {
			resetCount := 0
			user.DeleteHourlyCount = &resetCount
		}

		if user.DeleteHourlyCount != nil && user.LimitDeleteHourly != nil && *user.DeleteHourlyCount >= *user.LimitDeleteHourly {
			tx.Rollback()
			return c.JSON(http.StatusForbidden, shared.ErrorResponse{Error: "Batas penghapusan per jam telah tercapai"})
		}
	}

	// --- 6. Find Server and Delete from Marzban ---
	var server server.Server
	err = tx.Where("kode = ?", genericAcc.KodeServer).First(&server).Error
	if err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Konfigurasi server untuk akun ini tidak ditemukan"})
	}

	domain := server.Domain
	if !strings.HasPrefix(domain, "http://") && !strings.HasPrefix(domain, "https://") {
		domain = "https://" + domain
	}
	var decryptedToken string
	decryptedToken, err = utils.Decrypt(server.Token)
	if err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses token server"})
	}

	apiURL := domain + "/api/user/" + genericAcc.Username
	var req *http.Request
	req, err = http.NewRequest("DELETE", apiURL, nil)
	if err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat permintaan penghapusan"})
	}
	req.Header.Set("Authorization", "Bearer "+decryptedToken)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		tx.Rollback()
		log.Printf("Gagal mengirim permintaan penghapusan ke Marzban untuk pengguna '%s': %v", genericAcc.Username, err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal berkomunikasi dengan server penyedia"})
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusNotFound {
		log.Printf("API Marzban mengembalikan status %d untuk penghapusan pengguna '%s'. Melanjutkan dengan penghapusan lokal.", resp.StatusCode, genericAcc.Username)
	}

	// --- 7. Update Status and Delete Local Account Record ---
	// First, explicitly update the status to "deleted"
	if err := tx.Model(accountToDelete).Update("status", "deleted").Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui status akun"})
	}

	// Then, perform a soft delete by setting the `deleted_at` field
	if err := tx.Delete(accountToDelete).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menghapus akun dari database"})
	}

	// --- 8. Update User's Delete Count (if not admin) ---
	if role != "admin" {
		var user user.User
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&user, userID).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data pengguna untuk memperbarui jumlah penghapusan"})
		}

		now := time.Now()
		newCount := 1
		if user.DeleteHourlyCount != nil && user.LastDeleteHourlyAt != nil && user.LastDeleteHourlyAt.Year() == now.Year() && user.LastDeleteHourlyAt.Month() == now.Month() && user.LastDeleteHourlyAt.Day() == now.Day() && user.LastDeleteHourlyAt.Hour() == now.Hour() {
			newCount = *user.DeleteHourlyCount + 1
		}

		user.DeleteHourlyCount = &newCount
		user.LastDeleteHourlyAt = &now

		if err := tx.Save(&user).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui jumlah penghapusan pengguna"})
		}
	}

	// --- 9. Commit Transaction ---
	if err := tx.Commit().Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyelesaikan transaksi"})
	}

	return c.JSON(http.StatusOK, shared.SuccessResponse{Message: "Akun berhasil dihapus"})
}
