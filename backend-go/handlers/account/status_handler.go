package account

import (
	"fmt"
	"math"
	"net/http"
	"strconv"
	"strings"
	"time"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/middleware"
	"vpn-shop/backend-go/models/account"
	"vpn-shop/backend-go/models/payment"
	serverModel "vpn-shop/backend-go/models/server"
	"vpn-shop/backend-go/models/shared"
	userModel "vpn-shop/backend-go/models/user"
	"vpn-shop/backend-go/tasks"
	"vpn-shop/backend-go/utils"

	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// UpdateAccountStatus is the handler for activating or disabling an account.
func UpdateAccountStatus(c echo.Context, newStatus string) error {
	// --- 1. Get User Info from JWT ---
	userIDStr, err := middleware.GetUserIDStringFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	// --- 2. Parse Input ---
	accountIDStr := c.Param("account_id")
	accountID, err := strconv.ParseUint(accountIDStr, 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format ID akun tidak valid"})
	}
	accountType := c.QueryParam("account_type")

	// --- 3. Database Transaction ---
	tx := db.DB.Begin()
	if tx.Error != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memulai transaksi"})
	}
	defer tx.Rollback() // Rollback on any error

	// --- 4. Find Account & Authorize ---
	var genericAcc GenericAccount
	var accountToUpdate interface{}
	tableName, err := getTableNameFromType(accountType)
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: err.Error()})
	}

	handleError := func(err error) error {
		if err == gorm.ErrRecordNotFound {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Akun tidak ditemukan"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan database saat mencari akun"})
	}

	switch accountType {
	case "trojan":
		var acc account.AccountTrojan
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&acc, accountID).Error; err != nil {
			return handleError(err)
		}
		genericAcc = toGenericAccount(&acc)
		accountToUpdate = &acc
	case "vmess":
		var acc account.AccountVmess
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&acc, accountID).Error; err != nil {
			return handleError(err)
		}
		genericAcc = toGenericAccount(&acc)
		accountToUpdate = &acc
	case "vless":
		var acc account.AccountVless
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&acc, accountID).Error; err != nil {
			return handleError(err)
		}
		genericAcc = toGenericAccount(&acc)
		accountToUpdate = &acc
	case "ssh":
		var acc account.AccountSsh
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&acc, accountID).Error; err != nil {
			return handleError(err)
		}
		genericAcc = toGenericAccount(&acc)
		accountToUpdate = &acc
	default:
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Tipe akun yang ditentukan tidak valid"})
	}

	if userIDStr != genericAcc.UserID {
		return c.JSON(http.StatusForbidden, shared.ErrorResponse{Error: "Anda tidak memiliki izin untuk memodifikasi akun ini"})
	}

	// --- 5. Handle Hourly Billing Logic ---
	updateData := make(map[string]interface{})
	if genericAcc.IsHourly {
		now := time.Now()
		switch newStatus {
		case "disabled": // PAUSE
			if genericAcc.LastStart != nil {
				// Always calculate elapsed time and reset the stopwatch when pausing.
				durationSinceLastStart := now.Sub(*genericAcc.LastStart)
				minutesSinceLastStart := int(math.Ceil(durationSinceLastStart.Minutes()))

				totalUnbilledMinutes := genericAcc.UnbilledMinutes + minutesSinceLastStart
				hoursToBill := totalUnbilledMinutes / 60
				remainingUnbilledMinutes := totalUnbilledMinutes % 60

				updateData["unbilled_minutes"] = remainingUnbilledMinutes
				updateData["last_start"] = nil // Stop stopwatch

				if hoursToBill > 0 {
					// Billing logic is needed
					var user userModel.User
					if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&user, "id = ?", userIDStr).Error; err != nil {
						return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengunci pengguna untuk penagihan"})
					}
					harga, err := strconv.ParseInt(genericAcc.Harga, 10, 64)
					if err != nil {
						return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Format harga tidak valid"})
					}
					totalCost := harga * int64(hoursToBill)

					if user.Saldo >= totalCost {
						newBalance := user.Saldo - totalCost
						history := payment.BillingHourlyHistory{
							UserID:           genericAcc.UserID,
							AccountID:        genericAcc.ID,
							AccountType:      accountType,
							BilledAt:         now,
							HoursBilled:      hoursToBill,
							AmountBilled:     totalCost,
							InitialBalance:   user.Saldo,
							RemainingBalance: newBalance,
						}
						if err := tx.Create(&history).Error; err != nil {
							return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat riwayat penagihan"})
						}
						if err := tx.Model(&user).Update("saldo", newBalance).Error; err != nil {
							tx.Rollback()
							return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui saldo pengguna"})
						}
						updateData["last_billed_at"] = now
						updateData["total_hours"] = gorm.Expr("total_hours + ?", hoursToBill)

						// Convert generic account to the one used by tasks for logging
						taskAccount := toTaskGenericAccount(genericAcc, accountType)
						go tasks.LogBillingToCSV(user, taskAccount, history)
					}
				}
			}
		case "active": // PLAY
			updateData["last_start"] = now // Start stopwatch
		}
	}

	// --- 6. Apply Updates to Account ---
	if len(updateData) > 0 {
		if err := tx.Model(accountToUpdate).Updates(updateData).Error; err != nil {
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui detail penagihan akun"})
		}
	}

	// --- 7. Call Marzban and Update Local DB Status ---
	if err := updateMarzbanAndDBStatus(tx, genericAcc.Username, genericAcc.KodeServer, tableName, genericAcc.ID, newStatus); err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: err.Error()})
	}

	// --- 8. Commit Transaction ---
	if err := tx.Commit().Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyelesaikan transaksi"})
	}

	return c.JSON(http.StatusOK, shared.SuccessResponse{Message: fmt.Sprintf("Account %s successfully set to %s", genericAcc.Username, newStatus)})
}

// ActivateAccount godoc
// @Summary      Activate a user's account
// @Description  Sets an account's status to 'active' in both the local database and on the Marzban server.
// @Tags         Accounts
// @Accept       json
// @Produce      json
// @Param        account_id   path      string  true  "Account ID"
// @Param        account_type query     string  true  "Account Type" Enums(trojan, vmess, vless, ssh)
// @Success      200 {object} shared.SuccessResponse
// @Failure      400 {object} shared.ErrorResponse "Bad Request"
// @Failure      401 {object} shared.ErrorResponse "Unauthorized"
// @Failure      403 {object} shared.ErrorResponse "Forbidden"
// @Failure      404 {object} shared.ErrorResponse "Not Found"
// @Failure      500 {object} shared.ErrorResponse "Internal Server Error"
// @Security     OAuth2Password
// @Router       /accounts/{account_id}/activate [put]
func ActivateAccount(c echo.Context) error {
	return UpdateAccountStatus(c, "active")
}

// DisableAccount godoc
// @Summary      Disable a user's account
// @Description  Sets an account's status to 'disabled' on the Marzban server and 'suspended' in the local database.
// @Tags         Accounts
// @Accept       json
// @Produce      json
// @Param        account_id   path      string  true  "Account ID"
// @Param        account_type query     string  true  "Account Type" Enums(trojan, vmess, vless, ssh)
// @Success      200 {object} shared.SuccessResponse
// @Failure      400 {object} shared.ErrorResponse "Bad Request"
// @Failure      401 {object} shared.ErrorResponse "Unauthorized"
// @Failure      403 {object} shared.ErrorResponse "Forbidden"
// @Failure      404 {object} shared.ErrorResponse "Not Found"
// @Failure      500 {object} shared.ErrorResponse "Internal Server Error"
// @Security     OAuth2Password
// @Router       /accounts/{account_id}/disable [put]
func DisableAccount(c echo.Context) error {
	return UpdateAccountStatus(c, "disabled")
}

// updateMarzbanAndDBStatus handles the API call to Marzban and updates the local DB.
func updateMarzbanAndDBStatus(tx *gorm.DB, username, serverCode, tableName string, accountID uint, newStatus string) error {
	var server serverModel.Server
	if err := tx.Where("kode = ?", serverCode).First(&server).Error; err != nil {
		return fmt.Errorf("tidak dapat menemukan konfigurasi server '%s'", serverCode)
	}

	decryptedToken, err := utils.Decrypt(server.Token)
	if err != nil {
		return fmt.Errorf("gagal mendekripsi token server untuk %s", serverCode)
	}

	domain := server.Domain
	if !strings.HasPrefix(domain, "http://") && !strings.HasPrefix(domain, "https://") {
		domain = "https://" + domain
	}
	marzbanAPIURL := fmt.Sprintf("%s/api/user/%s", domain, username)

	// Marzban expects "active" or "disabled"
	payload := strings.NewReader(fmt.Sprintf(`{"status": "%s"}`, newStatus))

	req, err := http.NewRequest("PUT", marzbanAPIURL, payload)
	if err != nil {
		return fmt.Errorf("failed to create request for user %s", username)
	}
	req.Header.Add("Authorization", "Bearer "+decryptedToken)
	req.Header.Add("Content-Type", "application/json")
	req.Header.Add("accept", "application/json")

	client := &http.Client{Timeout: 10 * time.Second}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send request to Marzban for user %s", username)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("API marzban mengembalikan status non-OK (%d) untuk pengguna %s", resp.StatusCode, username)
	}

	// Determine local status based on newStatus
	// Our local DB uses "active" or "suspended"
	localStatus := newStatus
	if newStatus == "disabled" {
		localStatus = "suspended"
	}

	if err := tx.Table(tableName).Where("id = ?", accountID).Update("status", localStatus).Error; err != nil {
		return fmt.Errorf("gagal memperbarui status akun lokal: %w", err)
	}

	return nil
}

// getTableNameFromType maps an account type string to its table name.
func getTableNameFromType(accountType string) (string, error) {
	switch accountType {
	case "trojan":
		return "account_trojans", nil
	case "vmess":
		return "account_vmess", nil
	case "vless":
		return "account_vless", nil
	case "ssh":
		return "account_ssh", nil
	default:
		return "", fmt.Errorf("tipe akun tidak dikenal: %s", accountType)
	}
}
