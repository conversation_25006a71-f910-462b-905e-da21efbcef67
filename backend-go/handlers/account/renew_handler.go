package account

import (
	"errors"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/middleware"
	"vpn-shop/backend-go/models/account"
	"vpn-shop/backend-go/models/payment"
	"vpn-shop/backend-go/models/shared"
	userModel "vpn-shop/backend-go/models/user"
	"vpn-shop/backend-go/services"
	"vpn-shop/backend-go/utils"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
)

// RenewRequest mendefinisikan struktur untuk permintaan perpanjangan akun.
type RenewRequest struct {
	AccountID      uint   `json:"account_id" validate:"required"`
	AccountType    string `json:"account_type" validate:"required,oneof=trojan vmess vless ssh"`
	DurationMonths int    `json:"duration_months" validate:"required,min=1"`
	Pembayaran     string `json:"pembayaran" validate:"required,oneof=SALDO TRIPAY"`
	Metode         string `json:"metode" validate:"required"`
}

// GetAccountDetails mengambil detail akun dari tabel yang sesuai berdasarkan tipe akun.
func GetAccountDetails(accountID uint, accountType string) (account.IAccount, error) {
	var iAccount account.IAccount
	var err error

	switch accountType {
	case "trojan":
		var acc account.AccountTrojan
		err = db.DB.First(&acc, accountID).Error
		iAccount = &acc
	case "vmess":
		var acc account.AccountVmess
		err = db.DB.First(&acc, accountID).Error
		iAccount = &acc
	case "vless":
		var acc account.AccountVless
		err = db.DB.First(&acc, accountID).Error
		iAccount = &acc
	case "ssh":
		var acc account.AccountSsh
		err = db.DB.First(&acc, accountID).Error
		iAccount = &acc
	default:
		return nil, fmt.Errorf("tipe akun tidak valid: %s", accountType)
	}

	if err != nil {
		return nil, err
	}

	return iAccount, nil
}

// RenewAccount menangani logika untuk memperpanjang akun.
// RenewAccount godoc
// @Summary      Renew a VPN account
// @Description  Renews a user's VPN account for a specified number of months.
// @Tags         Accounts
// @Accept       json
// @Produce      json
// @Param        renew_request  body      RenewRequest  true  "Renew Request"
// @Success      200      {object}  shared.RenewSuccessResponse
// @Failure      400      {object}  shared.ErrorResponse
// @Failure      401      {object}  shared.ErrorResponse
// @Failure      403      {object}  shared.ErrorResponse
// @Failure      404      {object}  shared.ErrorResponse
// @Failure      422      {object}  shared.ErrorResponse
// @Failure      500      {object}  shared.ErrorResponse
// @Security     OAuth2Password
// @Router       /accounts/renew [post]
func RenewAccount(c echo.Context) error {
	// 1. Bind and Validate Request
	validate := validator.New()
	req := new(RenewRequest)
	if err := c.Bind(req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}
	if err := validate.Struct(req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: utils.FormatValidationError(err)})
	}

	// Additional validation for payment method
	if req.Pembayaran == "SALDO" && req.Metode != "user_saldo" {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Metode pembayaran tidak valid untuk SALDO"})
	}

	// 2. Get Authenticated User from JWT
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	var user userModel.User
	err = db.DB.First(&user, userID).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
		}
		log.Printf("Gagal mengambil pengguna %d: %v", userID, err)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data pengguna"})
	}

	// 3. Get Account Details
	acc, err := GetAccountDetails(req.AccountID, req.AccountType)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusUnprocessableEntity, shared.ErrorResponse{Error: "Akun tidak ditemukan atau akses ditolak"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil detail akun"})
	}

	// 4. Authorization Check: Pastikan pengguna adalah pemilik akun, HANYA jika akun sudah punya pemilik.
	accountOwnerID := acc.GetUserID()
	if accountOwnerID != "" && strconv.Itoa(int(user.ID)) != accountOwnerID {
		return c.JSON(http.StatusForbidden, shared.ErrorResponse{Error: "Anda tidak memiliki izin untuk memperpanjang akun ini"})
	}

	// 5. Check User Balance
	hargaPerBulan, err := strconv.Atoi(acc.GetHarga())
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Format harga akun tidak valid"})
	}

	totalHarga := hargaPerBulan * req.DurationMonths

	switch req.Pembayaran {
	case "SALDO":
		// --- PAYMENT WITH BALANCE ---
		if user.Saldo < int64(totalHarga) {
			return c.JSON(http.StatusPaymentRequired, shared.ErrorResponse{Error: "Saldo tidak mencukupi"})
		}

		tx := db.DB.Begin()
		if tx.Error != nil {
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memulai transaksi"})
		}

		accID := acc.GetID()
		accType := req.AccountType
		transaction := payment.Transaction{
			UserID:      user.ID,
			AccountID:   &accID,
			AccountType: &accType,
			Duration:    utils.IntToStringPointer(req.DurationMonths),
			Type:        payment.Renewal,
			Amount:      int64(totalHarga),
			Status:      payment.Success,
			Description: fmt.Sprintf("Perpanjangan Akun %s (%s) - %d bulan", acc.GetUsername(), acc.GetKodeAkun(), req.DurationMonths),
			InvoiceID:   fmt.Sprintf("INV-RENEW-%d", time.Now().UnixNano()),
		}
		if err := tx.Create(&transaction).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat catatan transaksi"})
		}

		newBalance := user.Saldo - int64(totalHarga)
		if err := tx.Model(&user).Updates(map[string]interface{}{
			"saldo":       newBalance,
			"pay_bulanan": gorm.Expr("pay_bulanan + ?", int64(totalHarga)),
			"total_pay":   gorm.Expr("total_pay + ?", int64(totalHarga)),
		}).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui saldo dan statistik pengguna"})
		}

		newExpiredDate := time.Now().AddDate(0, req.DurationMonths, 0)
		if acc.GetExpiredDate() != nil && acc.GetExpiredDate().After(time.Now()) {
			newExpiredDate = acc.GetExpiredDate().AddDate(0, req.DurationMonths, 0)
		}

		tableName := ""
		switch req.AccountType {
		case "trojan":
			tableName = "account_trojans"
		case "vmess":
			tableName = "account_vmess"
		case "vless":
			tableName = "account_vless"
		case "ssh":
			tableName = "account_ssh"
		}
		if err := tx.Table(tableName).Where("id = ?", acc.GetID()).Update("expired", newExpiredDate).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui masa aktif akun"})
		}

		if err := services.RenewMarzbanUser(c.Logger(), tx, acc.GetKodeServer(), acc.GetUsername(), newExpiredDate); err != nil {
			tx.Rollback()
			log.Printf("Gagal memperbarui pengguna di Marzban: %v", err)
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui akun di server. Silakan hubungi dukungan."})
		}

		if err := tx.Commit().Error; err != nil {
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyelesaikan transaksi"})
		}

		return c.JSON(http.StatusOK, shared.RenewSuccessResponse{
			Message:    "Akun berhasil diperpanjang",
			NewBalance: newBalance,
			NewExpire:  newExpiredDate.Format("2006-01-02"),
		})
	case "TRIPAY":
		// --- PAYMENT WITH TRIPAY GATEWAY ---
		invoiceID := "REN-" + strconv.FormatUint(uint64(userID), 10) + "-" + time.Now().Format("**************")
		tripayGateway := "TRIPAY"
		accID := acc.GetID()
		accType := req.AccountType

		transaction := payment.Transaction{
			UserID:         user.ID,
			Type:           payment.Renewal,
			Amount:         int64(totalHarga),
			Status:         payment.Pending,
			Description:    fmt.Sprintf("Perpanjangan Akun %s (%s) - %d bulan", acc.GetUsername(), acc.GetKodeAkun(), req.DurationMonths),
			InvoiceID:      invoiceID,
			PaymentGateway: &tripayGateway,
			AccountID:      &accID,
			AccountType:    &accType,
			Duration:       utils.IntToStringPointer(req.DurationMonths),
		}

		if err := db.DB.Create(&transaction).Error; err != nil {
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat catatan transaksi"})
		}

		checkoutURL, err := services.CreateTripayCheckoutURL(transaction, req.Metode, user, nil)
		if err != nil {
			db.DB.Model(&transaction).Update("status", payment.Failed)
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat pembayaran", Details: err.Error()})
		}

		if err := db.DB.Model(&transaction).Updates(map[string]interface{}{
			"gateway_reference":    checkoutURL.Reference,
			"gateway_checkout_url": checkoutURL.CheckoutURL,
		}).Error; err != nil {
			log.Printf("Gagal memperbarui referensi gateway untuk transaksi %s: %v", invoiceID, err)
		}

		return c.JSON(http.StatusOK, map[string]interface{}{
			"checkout_url":      checkoutURL.CheckoutURL,
			"gateway_reference": checkoutURL.Reference,
			"invoice_id":        transaction.InvoiceID,
		})
	default:
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Jenis pembayaran tidak didukung"})
	}
}
