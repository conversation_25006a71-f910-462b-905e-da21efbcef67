package account

import (
	"time"
	"vpn-shop/backend-go/models/account"
	"vpn-shop/backend-go/tasks"
)

// GenericAccount holds common fields from different account models for processing.
// This avoids using reflection and keeps logic cleaner.
type GenericAccount struct {
	ID              uint
	UserID          string
	Username        string
	KodeServer      string
	IsHourly        bool
	LastStart       *time.Time
	UnbilledMinutes int
	Harga           string
	CreatedAt       time.Time
}

// toGenericAccount converts a specific account model to the GenericAccount struct.
func toGenericAccount(acc interface{}) GenericAccount {
	switch v := acc.(type) {
	case *account.AccountTrojan:
		return GenericAccount{
			ID: v.AccountID, UserID: v.UserID, Username: v.Username, KodeServer: v.KodeServer,
			IsHourly: v.<PERSON>ourly, LastStart: v.LastStart, UnbilledMinutes: v.UnbilledMinutes, Harga: v.Harga, CreatedAt: v.CreatedAt,
		}
	case *account.AccountVmess:
		return GenericAccount{
			ID: v.AccountID, UserID: v.UserID, Username: v.<PERSON>rna<PERSON>, KodeServer: v.KodeServer,
			IsHourly: v.<PERSON>, LastStart: v.<PERSON>Start, UnbilledMinutes: v.UnbilledMinutes, Harga: v.Harga, CreatedAt: v.CreatedAt,
		}
	case *account.AccountVless:
		return GenericAccount{
			ID: v.AccountID, UserID: v.UserID, Username: v.Username, KodeServer: v.KodeServer,
			IsHourly: v.IsHourly, LastStart: v.LastStart, UnbilledMinutes: v.UnbilledMinutes, Harga: v.Harga, CreatedAt: v.CreatedAt,
		}
	case *account.AccountSsh:
		return GenericAccount{
			ID: v.AccountID, UserID: v.UserID, Username: v.Username, KodeServer: v.KodeServer,
			IsHourly: v.IsHourly, LastStart: v.LastStart, UnbilledMinutes: v.UnbilledMinutes, Harga: v.Harga, CreatedAt: v.CreatedAt,
		}
	default:
		return GenericAccount{}
	}
}

// toTaskGenericAccount converts our local GenericAccount to the one used by the tasks package for CSV logging.
func toTaskGenericAccount(acc GenericAccount, accType string) tasks.GenericHourlyAccount {
	return tasks.GenericHourlyAccount{
		ID:           acc.ID,
		UserID:       acc.UserID,
		Username:     acc.Username,
		IsHourly:     acc.IsHourly,
		LastBilledAt: nil, // This can be adjusted if needed, but for deletion log it's ok
		Harga:        acc.Harga,
		AccountType:  accType,
		KodeServer:   acc.KodeServer,
		CreatedAt:    acc.CreatedAt,
	}
}
