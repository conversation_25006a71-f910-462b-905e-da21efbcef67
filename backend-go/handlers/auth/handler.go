package auth

import (
	"bytes"
	"errors"
	"fmt"
	"io"
	"log"
	"net/http"
	"strings"
	"time"

	"vpn-shop/backend-go/core"
	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/models/shared"
	user "vpn-shop/backend-go/models/user"
	utils "vpn-shop/backend-go/utils"

	"github.com/golang-jwt/jwt/v5"
	"github.com/labstack/echo/v4"
	"golang.org/x/crypto/bcrypt"
	"gorm.io/gorm"
)

type RegisterRequest struct {
	Name     string `json:"name" validate:"required" example:"John <PERSON>"`
	Username string `json:"username" validate:"required,alphanumdash,no-edge-hyphens,min-alphanum-len=6" example:"johndoe"`
	Email    string `json:"email" validate:"required,email" example:"<EMAIL>"`
	Password string `json:"password" validate:"required,min=6" example:"password123"`
	Whatsapp string `json:"whatsapp" validate:"required,min=10,numeric" example:"************"`
}

// Register godoc
// @Summary Register a new user
// @Description Create a new user account
// @Tags auth
// @Accept  json
// @Produce  json
// @Param   user  body   RegisterRequest  true  "User registration info"
// @Success 201 {object} map[string]interface{}
// @Failure 400 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /auth/register [post]
// TelegramAuthRequest defines the structure for data received from Telegram Login Widget.
// Note: Field names must be snake_case to match Telegram's response.
type TelegramAuthRequest struct {
	ID        int64  `json:"id" validate:"required"`
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
	Username  string `json:"username"`
	PhotoURL  string `json:"photo_url"`
	AuthDate  int64  `json:"auth_date" validate:"required"`
	Hash      string `json:"hash" validate:"required"`
}

// TelegramAuth godoc
// @Summary Authenticate or Register a user via Telegram
// @Description Authenticates a user with a Telegram ID. If the user doesn't exist, it creates a new account.
// @Tags auth
// @Accept  json
// @Produce  json
// @Param   user  body   TelegramAuthRequest  true  "Telegram User auth info"
// @Success 200 {object} map[string]string
// @Success 201 {object} map[string]string
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 409 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /auth/telegram [post]
func TelegramAuth(c echo.Context) error {
	// DEBUG: Mencetak raw body dari request
	bodyBytes, _ := io.ReadAll(c.Request().Body)
	log.Printf("[DEBUG] Telegram Auth Payload: %s", string(bodyBytes))
	// Reset body agar bisa dibaca lagi oleh c.Bind()
	c.Request().Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 1. Bind and Validate Request
	var req TelegramAuthRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Request tidak valid"})
	}
	if err := utils.Validate.Struct(req); err != nil {
		formattedErrors := utils.FormatValidationErrors(err)
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Data tidak valid", Details: formattedErrors})
	}

	// 2. Verify Telegram Hash
	cfg := core.LoadConfig()
	if !utils.VerifyTelegramHash(req.Hash, req, cfg.TelegramBotToken) {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Verifikasi data Telegram gagal. Hash tidak cocok."})
	}

	// 3. Check if user exists by Telegram ID
	var u user.User
	telegramIdStr := utils.Int64ToString(req.ID)

	err := db.DB.Where("telegram = ?", telegramIdStr).First(&u).Error
	if err == nil {
		// --- User Exists: Log them in ---
		return generateToken(c, &u)
	}

	// --- User Does Not Exist: Register them ---
	if !errors.Is(err, gorm.ErrRecordNotFound) {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Terjadi kesalahan database saat memeriksa pengguna"})
	}

	// Generate a secure random password
	randomPassword, err := utils.GenerateRandomString(32)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat data internal yang aman"})
	}
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(randomPassword), bcrypt.DefaultCost)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses data internal"})
	}

	// Prepare user data
	fullName := strings.TrimSpace(req.FirstName + " " + req.LastName)
	email := fmt.Sprintf("%<EMAIL>", req.ID)

	// Determine the system username based on user's request.
	// Use Telegram username if it exists, otherwise use Telegram ID as a unique fallback.
	systemUsername := req.Username
	if systemUsername == "" {
		systemUsername = telegramIdStr
	}

	// Create new user
	newUser := user.User{
		Name:          fullName,
		Username:      systemUsername, // Use Telegram username if available, otherwise fallback to ID.
		Email:         &email,
		Password:      string(hashedPassword),
		Telegram:      &telegramIdStr,
		UserTelegram:  &req.Username, // Always store the original Telegram username for reference.
		EmailVerified: 1,             // Auto-verify users from Telegram
		VerifWa:       1,             // Auto-verify users from Telegram
	}

	err = db.DB.Transaction(func(tx *gorm.DB) error {
		if txErr := tx.Create(&newUser).Error; txErr != nil {
			return txErr
		}

		// Generate default avatar for the new user
		avatarPath, avatarErr := utils.GenerateColoredAvatar(newUser.ID, newUser.Name)
		if avatarErr != nil {
			log.Printf("Peringatan: Gagal membuat avatar default untuk pengguna %s: %v", newUser.Username, avatarErr)
		} else {
			// Update user with avatar path
			if updateErr := tx.Model(&newUser).Update("path_photo", avatarPath).Error; updateErr != nil {
				log.Printf("Peringatan: Gagal menyimpan path avatar untuk pengguna %s: %v", newUser.Username, updateErr)
			}
		}

		var defaultRole user.Role
		if roleErr := tx.Where("name = ?", "member").First(&defaultRole).Error; roleErr != nil {
			log.Printf("Peringatan: Tidak dapat menemukan peran default 'member' untuk pengguna baru %s: %v", newUser.Username, roleErr)
			return nil
		}
		if assocErr := tx.Model(&newUser).Association("Roles").Append(&defaultRole); assocErr != nil {
			log.Printf("Peringatan: Gagal menetapkan peran default ke pengguna baru %s: %v", newUser.Username, assocErr)
		}
		return nil
	})

	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat pengguna", Details: err.Error()})
	}

	// --- New User Created: Log them in ---
	return generateToken(c, &newUser)
}

// generateToken creates a JWT for a given user and returns it.
func generateToken(c echo.Context, u *user.User) error {
	cfg := core.LoadConfig()

	// Create token claims
	claims := jwt.MapClaims{
		"user_id":  u.ID,
		"username": u.Username,
		"name":     u.Name,
		"email":    u.Email,
		"exp":      time.Now().Add(time.Hour * 72).Unix(), // Token expires in 72 hours
	}

	// Create token with claims
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)

	// Generate encoded token and send it as response
	t, err := token.SignedString([]byte(cfg.JWTSecretKey))
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat token autentikasi"})
	}

	// Memuat relasi Roles secara eksplisit untuk memastikan ada di response
	if err := db.DB.Preload("Roles").First(&u, u.ID).Error; err != nil {
		c.Logger().Errorf("Gagal memuat peran untuk pengguna %d: %v", u.ID, err)
		// Tetap lanjutkan meskipun gagal memuat peran, mungkin user tidak punya
	}

	// Buat struktur respons yang bersih untuk frontend
	userResponse := u.ToUserResponse()

	return c.JSON(http.StatusOK, map[string]interface{}{
		"user":        userResponse,
		"accessToken": t,
	})
}

// Register godoc
// @Summary Register a new user
// @Description Create a new user account with username, email, and password.
// @Tags auth
// @Accept  json
// @Produce  json
// @Param   user  body   RegisterRequest  true  "User registration info"
// @Success 201 {object} map[string]interface{}
// @Failure 400 {object} shared.ErrorResponse
// @Failure 409 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /auth/register [post]
func Register(c echo.Context) error {
	var req RegisterRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	// Validate request
	if err := c.Validate(req); err != nil {
		formattedErrors := utils.FormatValidationError(err)
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Data tidak valid", Details: formattedErrors})
	}

	// Check for existing user before hashing password
	var existingUser user.User
	if err := db.DB.Where("username = ? OR email = ?", req.Username, req.Email).First(&existingUser).Error; err == nil {
		if existingUser.Username == req.Username {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Nama pengguna sudah digunakan"})
		}
		if existingUser.Email != nil && *existingUser.Email == req.Email {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Email sudah terdaftar"})
		}
	} else if !errors.Is(err, gorm.ErrRecordNotFound) {
		// Handle potential database errors during the check
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Terjadi kesalahan database saat memeriksa pengguna yang sudah ada"})
	}

	// Hash the password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengenkripsi password"})
	}

	// Create user
	newUser := user.User{
		Name:     req.Name,
		Username: req.Username,
		Email:    &req.Email,
		Password: string(hashedPassword),
		Whatsapp: &req.Whatsapp,
	}

	err = db.DB.Transaction(func(tx *gorm.DB) error {
		// Create the user record
		if txErr := tx.Create(&newUser).Error; txErr != nil {
			return txErr
		}

		// Generate default avatar for the new user
		avatarPath, avatarErr := utils.GenerateColoredAvatar(newUser.ID, newUser.Name)
		if avatarErr != nil {
			log.Printf("Peringatan: Gagal membuat avatar default untuk pengguna %s: %v", newUser.Username, avatarErr)
		} else {
			// Update user with avatar path
			if updateErr := tx.Model(&newUser).Update("path_photo", avatarPath).Error; updateErr != nil {
				log.Printf("Peringatan: Gagal menyimpan path avatar untuk pengguna %s: %v", newUser.Username, updateErr)
			}
		}

		// Find the default role
		var defaultRole user.Role
		if roleErr := tx.Where("name = ?", "member").First(&defaultRole).Error; roleErr != nil {
			// Log the warning but do not fail the transaction, so the user is still created.
			log.Printf("Peringatan: Tidak dapat menemukan peran default 'member' untuk pengguna baru %s: %v", newUser.Username, roleErr)
			return nil
		}

		// Associate the user with the default role
		if assocErr := tx.Model(&newUser).Association("Roles").Append(&defaultRole); assocErr != nil {
			// Log the warning but do not fail the transaction
			log.Printf("Peringatan: Gagal menetapkan peran default ke pengguna baru %s: %v", newUser.Username, assocErr)
		}

		return nil // Commit the transaction
	})

	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat pengguna", Details: err.Error()})
	}

	return generateToken(c, &newUser)
}

type LoginRequest struct {
	Identifier string `json:"identifier" validate:"required"`
	Password   string `json:"password" validate:"required"`
}

// Login godoc
// @Summary Log in a user
// @Description Authenticate a user and get a token. Used by Swagger's OAuth2 flow.
// @Tags auth
// @Accept  application/x-www-form-urlencoded
// @Accept  application/json
// @Produce  json
// @Param   identifier formData string true "Username or Email"
// @Param   password formData string true "Password"
// @Success 200 {object} map[string]string "{\"token\": \"...\"}"
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /auth/login [post]
func Login(c echo.Context) error {
	identifier := ""
	password := ""
	contentType := c.Request().Header.Get(echo.HeaderContentType)

	// Handle JSON for standard API clients
	if strings.HasPrefix(contentType, echo.MIMEApplicationJSON) {
		var req LoginRequest
		if err := c.Bind(&req); err != nil {
			return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan JSON tidak valid"})
		}
		identifier = req.Identifier
		password = req.Password
	} else { // Handle form-data for Swagger/OAuth2
		identifier = c.FormValue("identifier") // Swagger sends 'identifier'
		password = c.FormValue("password")
	}

	if identifier == "" || password == "" {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Nama pengguna/email atau password tidak boleh kosong"})
	}

	// Find user by email or username
	var user user.User
	var query string

	if strings.Contains(identifier, "@") {
		query = "email = ?"
	} else {
		query = "username = ?"
	}

	if result := db.DB.Where(query, identifier).First(&user); result.Error != nil {
		if errors.Is(result.Error, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Kredensial tidak valid"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Terjadi kesalahan database"})
	}

	// Compare password
	if err := bcrypt.CompareHashAndPassword([]byte(user.Password), []byte(password)); err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: "Kredensial tidak valid"})
	}

	if err := db.DB.Model(&user).Update("last_login", time.Now()).Error; err != nil {
		c.Logger().Errorf("gagal memperbarui waktu login terakhir untuk pengguna %d: %v", user.ID, err)
	}

	return generateToken(c, &user)
}
