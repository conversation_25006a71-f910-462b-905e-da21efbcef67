package notification

import (
	"net/http"
	"strconv"

	"vpn-shop/backend-go/middleware"
	"vpn-shop/backend-go/models/notification"
	"vpn-shop/backend-go/models/shared"
	"vpn-shop/backend-go/services"

	"github.com/labstack/echo/v4"
	log "github.com/sirupsen/logrus"
)

// GetUserNotifications retrieves notifications for the authenticated user
// @Summary Get user notifications
// @Description Get paginated list of notifications for the authenticated user
// @Tags Notifications
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param page query int false "Page number" default(1)
// @Param limit query int false "Items per page" default(10)
// @Success 200 {object} notification.NotificationListResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /users/me/notifications [get]
func GetUserNotifications(c echo.Context) error {
	// Get user ID from JWT token
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	// Parse query parameters
	page := 1
	limit := 10

	if pageStr := c.QueryParam("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	if limitStr := c.QueryParam("limit"); limitStr != "" {
		if l, err := strconv.Atoi(limitStr); err == nil && l > 0 && l <= 100 {
			limit = l
		}
	}

	// Get notifications
	service := services.NewNotificationService()
	notifications, err := service.GetUserNotifications(userID, page, limit)
	if err != nil {
		log.WithError(err).Error("Failed to get user notifications")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil notifikasi"})
	}

	return c.JSON(http.StatusOK, notifications)
}

// MarkNotificationsAsRead marks specific notifications as read
// @Summary Mark notifications as read
// @Description Mark specific notifications as read for the authenticated user
// @Tags Notifications
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body notification.MarkAsReadRequest true "Notification IDs to mark as read"
// @Success 200 {object} shared.SuccessResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /users/me/notifications/mark-read [post]
func MarkNotificationsAsRead(c echo.Context) error {
	// Get user ID from JWT token
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	// Parse request body
	var req notification.MarkAsReadRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	if len(req.NotificationIDs) == 0 {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "ID notifikasi tidak boleh kosong"})
	}

	// Mark notifications as read
	service := services.NewNotificationService()
	if err := service.MarkAsRead(userID, req.NotificationIDs); err != nil {
		log.WithError(err).Error("Failed to mark notifications as read")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menandai notifikasi sebagai dibaca"})
	}

	return c.JSON(http.StatusOK, shared.SuccessResponse{
		Success: true,
		Message: "Notifikasi berhasil ditandai sebagai dibaca",
	})
}

// MarkAllNotificationsAsRead marks all notifications as read
// @Summary Mark all notifications as read
// @Description Mark all notifications as read for the authenticated user
// @Tags Notifications
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} shared.SuccessResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /users/me/notifications/mark-all-read [post]
func MarkAllNotificationsAsRead(c echo.Context) error {
	// Get user ID from JWT token
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	// Mark all notifications as read
	service := services.NewNotificationService()
	if err := service.MarkAllAsRead(userID); err != nil {
		log.WithError(err).Error("Failed to mark all notifications as read")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menandai semua notifikasi sebagai dibaca"})
	}

	return c.JSON(http.StatusOK, shared.SuccessResponse{
		Success: true,
		Message: "Semua notifikasi berhasil ditandai sebagai dibaca",
	})
}

// GetNotificationStats gets notification statistics
// @Summary Get notification statistics
// @Description Get notification statistics (total and unread count) for the authenticated user
// @Tags Notifications
// @Security BearerAuth
// @Accept json
// @Produce json
// @Success 200 {object} notification.NotificationStatsResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /users/me/notifications/stats [get]
func GetNotificationStats(c echo.Context) error {
	// Get user ID from JWT token
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	// Get notification stats
	service := services.NewNotificationService()
	stats, err := service.GetNotificationStats(userID)
	if err != nil {
		log.WithError(err).Error("Failed to get notification stats")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil statistik notifikasi"})
	}

	return c.JSON(http.StatusOK, stats)
}

// DeleteNotification deletes a specific notification
// @Summary Delete notification
// @Description Delete a specific notification for the authenticated user
// @Tags Notifications
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param id path int true "Notification ID"
// @Success 200 {object} shared.SuccessResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /users/me/notifications/{id} [delete]
func DeleteNotification(c echo.Context) error {
	// Get user ID from JWT token
	userID, err := middleware.GetUserIDFromToken(c)
	if err != nil {
		return c.JSON(http.StatusUnauthorized, shared.ErrorResponse{Error: err.Error()})
	}

	// Parse notification ID
	notificationIDStr := c.Param("id")
	notificationID, err := strconv.ParseUint(notificationIDStr, 10, 32)
	if err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "ID notifikasi tidak valid"})
	}

	// Delete notification
	service := services.NewNotificationService()
	if err := service.DeleteNotification(userID, uint(notificationID)); err != nil {
		if err.Error() == "notification not found or not owned by user" {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Notifikasi tidak ditemukan"})
		}
		log.WithError(err).Error("Failed to delete notification")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menghapus notifikasi"})
	}

	return c.JSON(http.StatusOK, shared.SuccessResponse{
		Success: true,
		Message: "Notifikasi berhasil dihapus",
	})
}
