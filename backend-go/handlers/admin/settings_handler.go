package admin

import (
	"net/http"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/models/admin"
	"vpn-shop/backend-go/models/shared"
	"vpn-shop/backend-go/utils"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
)

// GetAdminSettings retrieves the current application settings.
// @Summary Get Admin Settings
// @Description Retrieves the current application-wide settings.
// @Tags Admin
// @Security BearerAuth
// @Produce  json
// @Success 200 {object} admin.AdminSetting
// @Failure 500 {object} shared.ErrorResponse
// @Router /admin/settings [get]
func GetAdminSettings(c echo.Context) error {
	settings, err := utils.GetSettings()
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil pengaturan"})
	}
	return c.JSON(http.StatusOK, settings)
}

// UpdateAdminSettings updates the application settings.
// @Summary Update Admin Settings
// @Description Updates the application-wide settings. All fields are required.
// @Tags Admin
// @Security BearerAuth
// @Accept   json
// @Produce  json
// @Param    settings body admin.AdminSetting true "New settings data"
// @Success 200 {object} admin.AdminSetting
// @Failure 400 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /admin/settings [put]
func UpdateAdminSettings(c echo.Context) error {
	var input admin.AdminSetting
	if err := c.Bind(&input); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	validate := validator.New()
	if err := validate.Struct(input); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: utils.FormatValidationError(err)})
	}

	// There should only be one row of settings, so we find it and update it.
	var settings admin.AdminSetting
	if err := db.DB.First(&settings).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Tidak dapat menemukan pengaturan untuk diperbarui"})
	}

	settings.ExpiredMinutes = input.ExpiredMinutes
	settings.DailyMtTime = input.DailyMtTime
	settings.HourlyBillingInterval = input.HourlyBillingInterval
	settings.MinSaldo = input.MinSaldo

	if err := db.DB.Save(&settings).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyimpan pengaturan"})
	}

	// Invalidate the cache after updating
	// A simple way is to just call GetSettings again to refresh it.
	go utils.GetSettings() // run in goroutine to not block response

	return c.JSON(http.StatusOK, settings)
}
