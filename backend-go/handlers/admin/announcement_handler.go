package admin

import (
	"net/http"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/models/announcement"
	"vpn-shop/backend-go/models/shared"
	"vpn-shop/backend-go/utils"

	"github.com/go-playground/validator/v10"
	"github.com/labstack/echo/v4"
)

// CreateAnnouncementRequest defines the request body for creating an announcement.
type CreateAnnouncementRequest struct {
	Judul string `json:"judul" validate:"required"`
	Isi   string `json:"isi" validate:"required"`
}

// UpdateAnnouncementRequest defines the request body for updating an announcement.
type UpdateAnnouncementRequest struct {
	Judul string `json:"judul" validate:"required"`
	Isi   string `json:"isi" validate:"required"`
}

// toAnnouncementResponse converts an Announcement model to an AnnouncementResponse DTO.
func toAnnouncementResponse(ann announcement.Announcement) announcement.AnnouncementResponse {
	return announcement.AnnouncementResponse{
		ID:        ann.<PERSON>,
		Judul:     ann.<PERSON>,
		Isi:       ann.<PERSON>i,
		CreatedAt: ann.<PERSON>,
		UpdatedAt: ann.<PERSON>t,
	}
}

// CreateAnnouncement membuat pengumuman baru.
// @Summary Create Announcement
// @Description Membuat pengumuman baru dengan judul dan isi.
// @Tags Admin
// @Security BearerAuth
// @Accept   json
// @Produce  json
// @Param    announcement body CreateAnnouncementRequest true "Data Pengumuman"
// @Success 201 {object} announcement.AnnouncementResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /admin/announcements [post]
func CreateAnnouncement(c echo.Context) error {
	var input CreateAnnouncementRequest
	if err := c.Bind(&input); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	validate := validator.New()
	if err := validate.Struct(input); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: utils.FormatValidationError(err)})
	}

	newAnnouncement := announcement.Announcement{
		Judul: input.Judul,
		Isi:   input.Isi,
	}

	if err := db.DB.Create(&newAnnouncement).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyimpan pengumuman"})
	}

	return c.JSON(http.StatusCreated, toAnnouncementResponse(newAnnouncement))
}

// UpdateAnnouncement memperbarui pengumuman yang ada.
// @Summary Update Announcement
// @Description Memperbarui judul atau isi dari pengumuman yang ada berdasarkan ID.
// @Tags Admin
// @Security BearerAuth
// @Accept   json
// @Produce  json
// @Param    id   path   int  true  "Announcement ID"
// @Param    announcement body UpdateAnnouncementRequest true "Data Pengumuman yang Diperbarui"
// @Success 200 {object} announcement.AnnouncementResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /admin/announcements/{id} [put]
func UpdateAnnouncement(c echo.Context) error {
	id := c.Param("id")
	var ann announcement.Announcement
	if err := db.DB.First(&ann, id).Error; err != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengumuman tidak ditemukan"})
	}

	var input UpdateAnnouncementRequest
	if err := c.Bind(&input); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	validate := validator.New()
	if err := validate.Struct(input); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: utils.FormatValidationError(err)})
	}

	ann.Judul = input.Judul
	ann.Isi = input.Isi

	if err := db.DB.Save(&ann).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui pengumuman"})
	}

	return c.JSON(http.StatusOK, toAnnouncementResponse(ann))
}

// DeleteAnnouncement menghapus pengumuman.
// @Summary Delete Announcement
// @Description Menghapus pengumuman berdasarkan ID.
// @Tags Admin
// @Security BearerAuth
// @Produce  json
// @Param    id   path   int  true  "Announcement ID"
// @Success 204 "No Content"
// @Failure 404 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /admin/announcements/{id} [delete]
func DeleteAnnouncement(c echo.Context) error {
	id := c.Param("id")
	var ann announcement.Announcement

	if err := db.DB.First(&ann, id).Error; err != nil {
		return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengumuman tidak ditemukan"})
	}

	if err := db.DB.Delete(&ann).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menghapus pengumuman"})
	}

	return c.NoContent(http.StatusNoContent)
}
