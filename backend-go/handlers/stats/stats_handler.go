package stats

import (
	"net/http"
	"vpn-shop/backend-go/db"

	"github.com/labstack/echo/v4"
)

// TopServerResponse adalah struct untuk data server terpopuler.
// swagger:model
type TopServerResponse struct {
	ServerKode    string `json:"server_kode"`
	ServerName    string `json:"server_name"`
	ServerCountry string `json:"server_country"`
	SlotServer    int    `json:"slot_server"`
	SlotTerpakai  int    `json:"slot_terpakai"`
	TotalUser     int    `json:"total_user"`
}

// GetTopPurchaseServers mengambil daftar server yang paling banyak dibeli.
// @Summary Get Top Purchase Servers
// @Description Mengambil daftar 5 server teratas yang paling banyak dibeli secara global.
// @Tags Public
// @Produce  json
// @Success 200 {array} TopServerResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /public/stats/top-servers [get]
func GetTopPurchaseServers(c echo.Context) error {
	var results []TopServerResponse

	// Mengambil data server dan mengurutkan<PERSON> berdasarkan slot_terpakai
	if err := db.DB.Table("servers").
		Select("kode as server_kode, nama as server_name, negara as server_country, slot_server, slot_terpakai, total_user").
		Order("slot_terpakai DESC").
		Limit(7).
		Scan(&results).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, map[string]string{"error": "Gagal mengambil data statistik server"})
	}

	return c.JSON(http.StatusOK, results)
}
