package purchase

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/labstack/echo/v4"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/models/account"
	"vpn-shop/backend-go/models/admin"
	"vpn-shop/backend-go/models/payment"
	"vpn-shop/backend-go/models/server"
	"vpn-shop/backend-go/models/shared"
	"vpn-shop/backend-go/models/user"
	"vpn-shop/backend-go/services"
	"vpn-shop/backend-go/utils"
)

// HourlyAccountResponse defines the specific fields for the account in the response.
type HourlyAccountResponse struct {
	AccountID        uint       `json:"account_id"`
	UserID           string     `json:"user_id"`
	KodeServer       string     `json:"kode_server"`
	KodeAkun         string     `json:"kode_akun"`
	Domain           string     `json:"domain"`
	Durasi           string     `json:"durasi"`
	Username         string     `json:"username"`
	UUID             string     `json:"uuid,omitempty"`
	Password         string     `json:"password,omitempty"`
	TanggalBeli      time.Time  `json:"tanggal_beli"`
	Status           string     `json:"status"`
	IsHourly         bool       `json:"is_hourly"`
	LastStart        *time.Time `json:"last_start"`
	UnbilledMinutes  int        `json:"unbilled_minutes"`
	TotalHours       int        `json:"total_hours"`
	SubscriptionType string     `json:"subscription_type"`
	Harga            string     `json:"harga"`
	CreatedAt        time.Time  `json:"created_at"`
	UpdatedAt        time.Time  `json:"updated_at"`
}

// PurchaseHourlySuccessResponse defines the response for a successful hourly purchase.
type PurchaseHourlySuccessResponse struct {
	NewBalance int64                 `json:"new_balance"`
	Account    HourlyAccountResponse `json:"account"`
	Message    string                `json:"message"`
}

// PurchaseHourlyRequest mendefinisikan struktur untuk permintaan pembelian per jam.
type PurchaseHourlyRequest struct {
	KodeServer string `json:"kode_server" validate:"required"`
	Protocol   string `json:"protocol" validate:"required,oneof=trojan vmess vless"`
	Username   string `json:"username" validate:"required,is-no-spaces,alphanumdash,no-edge-hyphens,min-alphanum-len=6"`
}

// PurchaseHourlyAccount menangani logika untuk membeli akun VPN per jam.
// @Summary Buy hourly accounts
// @Description Memproses pembelian akun VPN per jam baru.
// @Tags Purchase
// @Security BearerAuth
// @Accept  json
// @Produce  json
// @Param purchase body PurchaseHourlyRequest true "Data Pembelian Per Jam"
// @Success 201 {object} map[string]interface{}
// @Failure 400 {object} shared.ErrorResponse
// @Failure 402 {object} shared.ErrorResponse "Saldo tidak mencukupi"
// @Failure 404 {object} shared.ErrorResponse "Server tidak ditemukan"
// @Failure 409 {object} shared.ErrorResponse "Username sudah ada atau protokol tidak aktif"
// @Failure 500 {object} shared.ErrorResponse
// @Router /purchase/hourly [post]
func PurchaseHourlyAccount(c echo.Context) error {
	// 1. Dapatkan Pengguna dari Token JWT
	userToken := c.Get("user").(*jwt.Token)
	claims := userToken.Claims.(*jwt.MapClaims)
	userID := uint((*claims)["user_id"].(float64))

	// 2. Bind dan Validasi Body Permintaan
	var req PurchaseHourlyRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format request tidak valid"})
	}
	if err := c.Validate(req); err != nil {
		formattedErrors := utils.FormatValidationError(err)
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Data tidak valid", Details: formattedErrors})
	}

	// 3. Dapatkan Data Server
	var serverData server.Server
	if err := db.DB.Where("kode = ?", req.KodeServer).First(&serverData).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Server tidak ditemukan"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mendapatkan detail server"})
	}

	// 4. Jalankan logika pembelian dengan saldo
	return handleHourlyPurchaseWithSaldo(c, userID, req, serverData)
}

func handleHourlyPurchaseWithSaldo(c echo.Context, userID uint, req PurchaseHourlyRequest, serverData server.Server) error {
	// Dapatkan data pengguna termasuk peran untuk menentukan harga
	var userData user.User
	if err := db.DB.Preload("Roles").First(&userData, userID).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mendapatkan data pengguna"})
	}

	// Dapatkan pengaturan admin untuk saldo minimal
	var adminSettings admin.AdminSetting
	if err := db.DB.First(&adminSettings).Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memuat pengaturan admin"})
	}

	var newAccountData account.IAccount

	// 1. Pemeriksaan awal untuk protokol & konfigurasi server
	switch req.Protocol {
	case "trojan":
		if serverData.Trojan == nil || *serverData.Trojan == "" || *serverData.Trojan == "disable" {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Protokol (trojan) tidak aktif di server ini"})
		}
	case "vmess":
		if serverData.Vmess == nil || *serverData.Vmess == "" || *serverData.Vmess == "disable" {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Protokol (vmess) tidak aktif di server ini"})
		}
	case "vless":
		if serverData.Vless == nil || *serverData.Vless == "" || *serverData.Vless == "disable" {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Protokol (vless) tidak aktif di server ini"})
		}
	}

	if serverData.Domain == "" || serverData.Token == "" {
		return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Konfigurasi API (domain/token) tidak tersedia di server ini"})
	}

	decryptedToken, err := utils.Decrypt(serverData.Token)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses token server"})
	}

	// 2. Pemeriksaan Koneksi API Eksternal
	if err := services.CheckAPIConnection(serverData.Domain, decryptedToken); err != nil {
		return c.JSON(http.StatusServiceUnavailable, shared.ErrorResponse{Error: fmt.Sprintf("API eksternal tidak dapat dijangkau: %v", err)})
	}

	// 3. Mulai Transaksi Database
	txErr := db.DB.Transaction(func(tx *gorm.DB) error {
		// Kunci baris pengguna untuk mencegah pembaruan serentak
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&userData, userID).Error; err != nil {
			return err
		}

		// Periksa apakah saldo pengguna mencukupi saldo minimal yang harus tersisa
		if userData.Saldo < adminSettings.MinSaldo {
			return fmt.Errorf("saldo Anda tidak mencukupi, saldo minimal yang harus dimiliki adalah %s", utils.FormatRupiah(adminSettings.MinSaldo))
		}

		// Tentukan harga bulanan berdasarkan peran pengguna
		var monthlyPrice int64
		isReseller := false
		for _, role := range userData.Roles {
			if role.Name == "RESELLER" {
				isReseller = true
				break
			}
		}

		if isReseller {
			monthlyPrice = serverData.HargaReseller
		} else {
			monthlyPrice = serverData.HargaMember
		}

		// Hitung harga per jam dari harga bulanan (asumsi 1 bulan = 30 hari = 720 jam)
		if monthlyPrice <= 0 {
			return fmt.Errorf("harga bulanan untuk server ini belum diatur atau tidak valid")
		}
		hourlyPrice := monthlyPrice / 720

		// Kunci baris pengguna untuk mencegah pembaruan serentak (opsional, karena saldo tidak diubah)
		if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).First(&userData, userID).Error; err != nil {
			return err
		}

		// 5. Validasi username unik (Marzban API)
		decryptedToken, err := utils.Decrypt(serverData.Token)
		if err != nil {
			return fmt.Errorf("gagal mendekripsi token server: %w", err)
		}
		marzbanUser, err := services.GetUser(serverData.Domain, decryptedToken, req.Username)
		if err != nil {
			// This is a technical error, not a 'not found' case, so we should fail.
			return fmt.Errorf("gagal memverifikasi ketersediaan username via API: %w", err)
		}
		if marzbanUser != nil {
			// User exists on Marzban, so it's a conflict.
			return fmt.Errorf("username '%s' sudah ada di server Marzban", req.Username)
		}

		// Siapkan data untuk API Marzban
		uuid, err := utils.GenerateUUID()
		if err != nil {
			return fmt.Errorf("gagal membuat uuid: %w", err)
		}

		var proxySettings map[string]interface{}
		switch req.Protocol {
		case "trojan":
			proxySettings = services.CreateTrojanProxySettings(uuid)
		case "vmess":
			proxySettings = services.CreateVmessProxySettings(uuid)
		case "vless":
			proxySettings = services.CreateVlessProxySettings(uuid)
		}

		// Untuk akun per jam, tidak ada Expire dan DataLimit
		payload := services.MarzbanUserRequest{
			Username: req.Username,
			Note:     "Akun Per Jam",
			Proxies:  map[string]interface{}{req.Protocol: proxySettings},
			Inbounds: map[string][]string{req.Protocol: {}},
			Status:   "active",
		}

		// Buat pengguna di Marzban
		err = services.CreateUser(serverData.Domain, decryptedToken, payload)
		if err != nil {
			return fmt.Errorf("gagal membuat pengguna di API eksternal: %w", err)
		}

		// Buat catatan akun lokal
		var kodeAkun string
		kodeAkun, err = utils.GenerateRandomString(10)
		if err != nil {
			return fmt.Errorf("gagal membuat kode akun: %w", err)
		}

		timeNow := time.Now()
		harga := strconv.FormatInt(hourlyPrice, 10)

		switch req.Protocol {
		case "trojan":
			newAccountData = &account.AccountTrojan{
				UserID: strconv.Itoa(int(userID)), KodeServer: req.KodeServer, KodeAkun: kodeAkun, Domain: serverData.Domain,
				Durasi: "Per Jam", Username: req.Username, UUID: uuid, TanggalBeli: timeNow, Status: "active",
				IsHourly: true, LastStart: &timeNow, TotalHours: 0, SubscriptionType: "hourly", Harga: harga,
			}
		case "vmess":
			newAccountData = &account.AccountVmess{
				UserID: strconv.Itoa(int(userID)), KodeServer: req.KodeServer, KodeAkun: kodeAkun, Domain: serverData.Domain,
				Durasi: "Per Jam", Username: req.Username, UUID: uuid, TanggalBeli: timeNow, Status: "active",
				IsHourly: true, LastStart: &timeNow, TotalHours: 0, SubscriptionType: "hourly", Harga: harga,
			}
		case "vless":
			newAccountData = &account.AccountVless{
				UserID: strconv.Itoa(int(userID)), KodeServer: req.KodeServer, KodeAkun: kodeAkun, Domain: serverData.Domain,
				Durasi: "Per Jam", Username: req.Username, UUID: uuid, TanggalBeli: timeNow, Status: "active",
				IsHourly: true, LastStart: &timeNow, TotalHours: 0, SubscriptionType: "hourly", Harga: harga,
			}
		}

		if err := tx.Save(&userData).Error; err != nil {
			return err
		}

		// Update server stats
		if err := tx.Model(&server.Server{}).Where("id = ?", serverData.ServerID).Updates(map[string]interface{}{
			"slot_terpakai": gorm.Expr("slot_terpakai + ?", 1),
			"total_user":    gorm.Expr("total_user + ?", 1),
		}).Error; err != nil {
			return fmt.Errorf("gagal memperbarui statistik server: %w", err)
		}

		// Update user payment statistics for hourly (PayPerjam will be updated by billing task)
		// For now, we don't update PayPerjam here since hourly billing is handled separately

		if err := tx.Create(newAccountData).Error; err != nil {
			return err
		}

		// Buat catatan transaksi
		accID := newAccountData.GetID()
		accType := req.Protocol
		transaction := payment.Transaction{
			InvoiceID: fmt.Sprintf("INV-HOURLY-%d-%s", userID, kodeAkun),
			UserID:    userID,
			AccountID: &accID, AccountType: &accType,
			Duration:    utils.StringToPointer("Per Jam"),
			Type:        payment.PurchaseHourly,
			Description: fmt.Sprintf("Pembelian akun %s per jam (%s)", req.Protocol, req.Username),
			Amount:      0, // Biaya awal adalah 0
			Status:      payment.Success,
		}
		if err := tx.Create(&transaction).Error; err != nil {
			return fmt.Errorf("gagal membuat catatan transaksi: %w", err)
		}

		return nil // Commit transaksi
	})

	if txErr != nil {
		if strings.Contains(txErr.Error(), "saldo tidak mencukupi") {
			return c.JSON(http.StatusPaymentRequired, shared.ErrorResponse{Error: txErr.Error()})
		}
		if strings.Contains(txErr.Error(), "sudah digunakan") {
			return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: txErr.Error()})
		}
		c.Logger().Errorf("Hourly purchase failed: %v", txErr)
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses pembelian: " + txErr.Error()})
	}

	// Create notification for successful hourly purchase
	if err := services.CreateHourlyPurchaseNotification(userID, req.Username, req.KodeServer); err != nil {
		c.Logger().Warnf("Failed to create hourly purchase notification for user %d: %v", userID, err)
		// Don't fail the request if notification fails
	}

	// Transaksi berhasil, siapkan respons yang bersih
	responseAccount := HourlyAccountResponse{
		AccountID:        newAccountData.GetID(),
		UserID:           newAccountData.GetUserID(),
		KodeServer:       newAccountData.GetKodeServer(),
		KodeAkun:         newAccountData.GetKodeAkun(),
		Domain:           newAccountData.GetDomain(),
		Durasi:           newAccountData.GetDurasi(),
		Username:         newAccountData.GetUsername(),
		UUID:             newAccountData.GetUUID(),
		Password:         newAccountData.GetPassword(),
		TanggalBeli:      newAccountData.GetTanggalBeli(),
		Status:           newAccountData.GetStatus(),
		IsHourly:         newAccountData.GetIsHourly(),
		LastStart:        newAccountData.GetLastStart(),
		SubscriptionType: newAccountData.GetSubscriptionType(),
		Harga:            newAccountData.GetHarga(),
		CreatedAt:        newAccountData.GetCreatedAt(),
		UpdatedAt:        newAccountData.GetUpdatedAt(),
		TotalHours:       newAccountData.GetTotalHours(),
	}

	return c.JSON(http.StatusCreated, PurchaseHourlySuccessResponse{
		Message:    "Akun VPN per jam berhasil dibuat",
		NewBalance: userData.Saldo,
		Account:    responseAccount,
	})
}
