package purchase

import (
	"time"
	"vpn-shop/backend-go/models/server"
)

// PublicAccountResponse defines the secure, public-facing data for a newly purchased account.
// It deliberately omits sensitive details.
type PublicAccountResponse struct {
	Username         string                      `json:"username"`
	AccountType      string                      `json:"account_type"`
	Status           string                      `json:"status"`
	Protocol         string                      `json:"protocol"`
	ExpiredDate      *time.Time                  `json:"expired_date"`
	SubscriptionType string                      `json:"subscription_type"`
	Server           server.PublicServerResponse `json:"server"`
}
