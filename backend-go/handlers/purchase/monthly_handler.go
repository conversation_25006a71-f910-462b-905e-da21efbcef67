package purchase

import (
	"errors"
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/labstack/echo/v4"
	log "github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"vpn-shop/backend-go/db"
	"vpn-shop/backend-go/models/account"
	"vpn-shop/backend-go/models/payment"
	serverModel "vpn-shop/backend-go/models/server"
	shared "vpn-shop/backend-go/models/shared"
	"vpn-shop/backend-go/models/user"
	"vpn-shop/backend-go/services"
	"vpn-shop/backend-go/utils"
)

// PurchaseMonthlyRequest defines the structure for a monthly purchase request.
type PurchaseMonthlyRequest struct {
	Username   string `json:"username" validate:"required,min=6,max=12"`
	Protocol   string `json:"protocol" validate:"required,oneof=trojan vmess vless"`
	Bulan      int    `json:"bulan" validate:"required,min=1,max=12"`
	KodeServer string `json:"kode_server" validate:"required"`
	Pembayaran string `json:"pembayaran" validate:"required,oneof=SALDO TRIPAY"`
	Metode     string `json:"metode" validate:"required"`
}

// PurchaseMonthlyHandler handles the logic for purchasing a monthly VPN account.
// @Summary Purchase Monthly VPN Account
// @Description Allows an authenticated user to purchase a new monthly VPN account.
// @Tags Purchase
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param purchase_request body PurchaseMonthlyRequest true "Purchase Request"
// @Success 200 {object} shared.SuccessResponse
// @Failure 400 {object} shared.ErrorResponse
// @Failure 401 {object} shared.ErrorResponse
// @Failure 404 {object} shared.ErrorResponse
// @Failure 409 {object} shared.ErrorResponse
// @Failure 500 {object} shared.ErrorResponse
// @Router /purchase/monthly [post]
func PurchaseMonthlyAccount(c echo.Context) error {
	// 1. Get user data from JWT
	token := c.Get("user").(*jwt.Token)
	claims, ok := token.Claims.(*jwt.MapClaims)
	if !ok {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses token"})
	}
	userIDFloat, ok := (*claims)["user_id"].(float64)
	if !ok {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membaca ID pengguna dari token"})
	}
	userID := uint(userIDFloat)

	// 2. Parse and validate request
	var req PurchaseMonthlyRequest
	if err := c.Bind(&req); err != nil {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Format permintaan tidak valid"})
	}

	if err := c.Validate(req); err != nil {
		formattedErrors := utils.FormatValidationError(err)
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Data tidak valid", Details: formattedErrors})
	}

	// 3. Additional validation for payment method
	if req.Pembayaran == "SALDO" && req.Metode != "user_saldo" {
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Metode pembayaran tidak valid untuk SALDO"})
	}

	// 4. Fetch user data from DB
	var userData user.User
	if err := db.DB.Preload("Roles").First(&userData, userID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Pengguna tidak ditemukan"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data pengguna"})
	}

	// 4. Fetch server data from DB
	var server serverModel.Server
	if err := db.DB.Where("kode = ?", req.KodeServer).First(&server).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return c.JSON(http.StatusNotFound, shared.ErrorResponse{Error: "Server tidak ditemukan"})
		}
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengambil data server"})
	}

	// 5. Check server status and availability
	if !server.DeletedAt.Time.IsZero() {
		return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Server tidak aktif atau telah dihapus"})
	}
	if server.SlotTerpakai != nil && *server.SlotTerpakai >= server.SlotServer {
		return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Server penuh"})
	}

	// 6. Check if username already exists on the server (across all protocols)
	exists, err := isUsernameExistsOnServer(db.DB, req.KodeServer, req.Username)
	if err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: err.Error()})
	}
	if exists {
		return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Username sudah ada di server ini"})
	}

	// 7. Check for existing PENDING transactions for the same username and server
	var existingPendingTransaction payment.Transaction
	descriptionQuery := fmt.Sprintf("%%Pembelian Akun %s(%s)%%", req.Username, req.KodeServer)
	if err := db.DB.Where("status = ? AND description LIKE ?", payment.Pending, descriptionQuery).First(&existingPendingTransaction).Error; err == nil {
		// A pending transaction was found
		return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Sudah ada transaksi yang tertunda untuk username ini. Harap selesaikan atau batalkan transaksi sebelumnya."})
	} else if err != gorm.ErrRecordNotFound {
		// An actual error occurred
		log.WithError(err).Error("Gagal memeriksa transaksi tertunda")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan internal server"})
	}

	// 8. Calculate price
	var harga int64
	if isUserReseller(userData) {
		harga = server.HargaReseller * int64(req.Bulan)
	} else {
		harga = server.HargaMember * int64(req.Bulan)
	}

	// 9. Route to the correct payment handler
	switch req.Pembayaran {
	case "SALDO":
		return handlePurchaseWithSaldo(c, req, userData, server, harga)
	case "TRIPAY":
		return handlePurchaseWithTripay(c, req, userData, server, harga)
	default:
		// This case should technically not be reached due to validation
		return c.JSON(http.StatusBadRequest, shared.ErrorResponse{Error: "Jenis pembayaran tidak didukung"})
	}
}

// isUsernameExistsOnServer checks if a username already exists on a specific server across all account tables.
func isUsernameExistsOnServer(tx *gorm.DB, kodeServer, username string) (bool, error) {
	accountTables := []string{"account_trojans", "account_vmess", "account_vless"}
	for _, table := range accountTables {
		var count int64
		if err := tx.Table(table).Where("kode_server = ? AND username = ?", kodeServer, username).Count(&count).Error; err != nil {
			// Log the error for debugging, but return a generic error to the caller
			log.WithError(err).Errorf("Gagal memeriksa username di tabel %s", table)
			return false, fmt.Errorf("gagal memverifikasi username di database")
		}
		if count > 0 {
			return true, nil // Username found
		}
	}
	return false, nil // Username does not exist on this server in any table
}

// isUserReseller checks if a user has the 'reseller' role.
func isUserReseller(user user.User) bool {
	for _, role := range user.Roles {
		if role.Name == "reseller" {
			return true
		}
	}
	return false
}

// handlePurchaseWithSaldo handles the logic for purchasing with account balance.
func handlePurchaseWithSaldo(c echo.Context, req PurchaseMonthlyRequest, user user.User, server serverModel.Server, harga int64) error {
	tx := db.DB.Begin()
	if tx.Error != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memulai transaksi"})
	}

	// 1. Check user balance
	if user.Saldo < harga {
		tx.Rollback()
		return c.JSON(http.StatusConflict, shared.ErrorResponse{Error: "Saldo tidak mencukupi"})
	}

	kodeAkun, err := utils.GenerateRandomString(10)
	if err != nil {
		tx.Rollback()
		log.WithError(err).Error("Gagal membuat kode akun acak")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan internal saat memproses permintaan"})
	}

	// 2. Deduct balance
	newBalance := user.Saldo - harga
	err = tx.Model(&user).Update("saldo", newBalance).Error
	if err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal mengurangi saldo"})
	}
	// 3. Create user on Marzban server
	// Decrypt the server token before using it
	decryptedToken, err := utils.Decrypt(server.Token)
	if err != nil {
		tx.Rollback()
		log.WithError(err).Error("Gagal mendekripsi token server")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan internal server"})
	}

	newPassword := uuid.New().String()
	expiryDate := time.Now().AddDate(0, req.Bulan, 0)

	var proxies map[string]interface{}
	switch req.Protocol {
	case "trojan":
		proxies = map[string]interface{}{"trojan": services.CreateTrojanProxySettings(newPassword)}
	case "vmess":
		proxies = map[string]interface{}{"vmess": services.CreateVmessProxySettings(newPassword)}
	case "vless":
		proxies = map[string]interface{}{"vless": services.CreateVlessProxySettings(newPassword)}
	}

	payload := services.MarzbanUserRequest{
		Username:               req.Username,
		Proxies:                proxies,
		Inbounds:               map[string][]string{req.Protocol: {}},
		Expire:                 expiryDate.Unix(),
		DataLimit:              1000 * 1024 * 1024 * 1024, // 1000 GB
		DataLimitResetStrategy: "no_reset",
		Status:                 "active",
		Note:                   fmt.Sprintf("Pembelian %d bulan", req.Bulan),
	}

	if err := services.CreateUser(server.Domain, decryptedToken, payload); err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat pengguna di Marzban", Details: err.Error()})
	}

	// 4. Create account record in local DB
	switch req.Protocol {
	case "trojan":
		acc := &account.AccountTrojan{
			UserID:           fmt.Sprintf("%d", user.ID),
			KodeServer:       req.KodeServer,
			KodeAkun:         kodeAkun,
			Domain:           server.Domain,
			Durasi:           fmt.Sprintf("%d", req.Bulan),
			Username:         req.Username,
			UUID:             newPassword,
			Expired:          &expiryDate,
			TanggalBeli:      time.Now(),
			Status:           "active",
			SubscriptionType: "montly",
			Harga:            fmt.Sprintf("%d", harga),
		}
		if err := tx.Create(acc).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyimpan akun"})
		}
	case "vmess", "vless":
		tableName := "account_vmess"
		if req.Protocol == "vless" {
			tableName = "account_vless"
		}
		acc := &account.AccountVmess{
			UserID:           fmt.Sprintf("%d", user.ID),
			KodeServer:       req.KodeServer,
			KodeAkun:         kodeAkun,
			Domain:           server.Domain,
			Durasi:           fmt.Sprintf("%d", req.Bulan),
			Username:         req.Username,
			UUID:             newPassword,
			Expired:          &expiryDate,
			TanggalBeli:      time.Now(),
			Status:           "active",
			SubscriptionType: "montly",
			Harga:            fmt.Sprintf("%d", harga),
		}
		if err := tx.Table(tableName).Create(acc).Error; err != nil {
			tx.Rollback()
			return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyimpan akun"})
		}
	}

	// 5. Create transaction record
	invoiceID := "SAL-" + strconv.FormatUint(uint64(user.ID), 10) + "-" + time.Now().Format("**************")
	saldoGateway := "SALDO"
	newTransaction := payment.Transaction{
		UserID:         user.ID,
		Amount:         harga,
		Description:    fmt.Sprintf("Pembelian Akun %s(%s) - %d bulan", req.Username, req.KodeServer, req.Bulan),
		Type:           payment.PurchaseMonthly,
		Status:         payment.Success,
		InvoiceID:      invoiceID,
		PaymentGateway: &saldoGateway,
		Duration:       utils.IntToStringPointer(req.Bulan),
	}
	if err := tx.Create(&newTransaction).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat transaksi"})
	}

	// 6. Update server stats
	if server.SlotTerpakai != nil {
		*server.SlotTerpakai++
	}
	if server.TotalUser != nil {
		*server.TotalUser++
	}
	if err := tx.Save(&server).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memperbarui statistik server"})
	}

	if err := tx.Commit().Error; err != nil {
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyelesaikan transaksi"})
	}

	return c.JSON(http.StatusOK, shared.SuccessResponse{Success: true, Message: "Pembelian berhasil"})
}

// handlePurchaseWithTripay handles the logic for purchasing with Tripay.
func handlePurchaseWithTripay(c echo.Context, req PurchaseMonthlyRequest, user user.User, server serverModel.Server, harga int64) error {
	// The payment method channel (e.g., QRIS, BCAVA) is now in req.Metode
	paymentMethod := req.Metode
	invoiceID := "TRIP-" + strconv.FormatUint(uint64(user.ID), 10) + "-" + time.Now().Format("**************")

	tx := db.DB.Begin()
	if tx.Error != nil {
		log.WithError(tx.Error).Error("Gagal memulai transaksi database")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan internal server"})
	}

	// Create a pending transaction record and order item within the same DB transaction
	tripayGateway := "TRIPAY"
	transaction := payment.Transaction{
		UserID:         user.ID,
		Amount:         harga,
		Description:    fmt.Sprintf("Pembelian Akun %s(%s) - %d bulan", req.Username, req.KodeServer, req.Bulan),
		Type:           payment.PurchaseMonthly,
		Status:         payment.Pending,
		InvoiceID:      invoiceID,
		PaymentGateway: &tripayGateway,
		Duration:       utils.IntToStringPointer(req.Bulan),
	}

	if err := tx.Create(&transaction).Error; err != nil {
		tx.Rollback()
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal membuat catatan transaksi"})
	}

	// Create OrderItems for Tripay, embedding purchase data in SKU for the webhook
	sku := fmt.Sprintf("PURCHASE|%s|%s|%s|%d", req.KodeServer, req.Protocol, req.Username, req.Bulan)
	orderItem := payment.OrderItem{
		SKU:           sku,
		Name:          fmt.Sprintf("Akun %s %s", req.Protocol, server.Negara),
		Price:         harga,
		Quantity:      1,
		MerchantRef:   invoiceID,
		TransactionID: transaction.ID,
	}

	if err := tx.Create(&orderItem).Error; err != nil {
		tx.Rollback()
		log.WithError(err).Error("Gagal menyimpan order item")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal memproses permintaan"})
	}

	// Convert database model to service DTO for the API call
	tripayItems := []services.OrderItem{
		{
			SKU:      orderItem.SKU,
			Name:     orderItem.Name,
			Price:    int(orderItem.Price), // Cast int64 to int for the service
			Quantity: orderItem.Quantity,
		},
	}

	// Create Tripay checkout URL
	checkoutURL, err := services.CreateTripayCheckoutURL(transaction, paymentMethod, user, tripayItems)
	if err != nil {
		tx.Rollback()
		log.WithError(err).Error("Gagal membuat URL checkout Tripay")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal berkomunikasi dengan payment gateway"})
	}

	// Update transaction with gateway reference
	if err := tx.Model(&transaction).Updates(map[string]interface{}{
		"gateway_reference":    checkoutURL.Reference,
		"gateway_checkout_url": checkoutURL.CheckoutURL,
	}).Error; err != nil {
		tx.Rollback()
		log.WithError(err).Error("Gagal memperbarui transaksi dengan referensi gateway")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Gagal menyimpan detail pembayaran"})
	}

	if err := tx.Commit().Error; err != nil {
		tx.Rollback()
		log.WithError(err).Error("Gagal menyimpan transaksi ke database")
		return c.JSON(http.StatusInternalServerError, shared.ErrorResponse{Error: "Kesalahan internal server"})
	}

	return c.JSON(http.StatusOK, map[string]interface{}{
		"checkout_url":      checkoutURL.CheckoutURL,
		"gateway_reference": checkoutURL.Reference,
		"invoice_id":        transaction.InvoiceID,
	})
}
