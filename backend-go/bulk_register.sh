#!/bin/bash

# URL endpoint registrasi untuk backend Go
API_URL="http://localhost:8000/api/v1/auth/register"
HEADERS="Content-Type: application/json"

echo "<PERSON><PERSON> proses registrasi 10 pengguna baru untuk backend Go..."

for i in {1..10}
do
    # Membuat data JSON untuk setiap pengguna
    USER_DATA=$(cat <<EOF
{
    "name": "go_testuser${i}",
    "username": "go_testuser${i}",
    "email": "go_testuser${i}@example.com",
    "password": "password123",
    "whatsapp": "0898765432${i}"
}
EOF
)

    echo "Mendaftarkan: go_testuser${i}..."
    
    # Mengirim permintaan POST menggunakan curl. Menambahkan \n sebelum http_code untuk memastikan pemisahan yang andal.
    response=$(curl -s -w "\n%{http_code}" -X POST "$API_URL" \
         -H "$HEADERS" \
         -d "$USER_DATA")
    
    # Ekstrak HTTP status code dan body
    http_code=$(tail -n1 <<< "$response")
    body=$(sed '$ d' <<< "$response")

    # Memeriksa kode status 201 (Created) atau 200 (OK)
    if [ "$http_code" -eq 201 ] || [ "$http_code" -eq 200 ]; then
        echo "  -> Berhasil! Pengguna 'go_testuser${i}' terdaftar. Status: $http_code"
        echo "     Respons: $body"
    else
        echo "  -> Gagal! Status: $http_code, Detail: $body"
    fi
done

echo -e "\nProses registrasi selesai."
