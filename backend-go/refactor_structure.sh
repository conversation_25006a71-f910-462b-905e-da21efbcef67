#!/bin/bash

# <PERSON>ript to refactor the Go backend structure by moving files into feature-based directories.

# Define base directories
BASE_DIR="/home/<USER>/Project/vpn-shop/backend-go"
HANDLERS_DIR="$BASE_DIR/handlers"
API_DIR="$BASE_DIR/api"

# Function to move a file if it exists
move_if_exists() {
    local src=$1
    local dest=$2
    if [ -f "$src" ]; then
        mv "$src" "$dest"
        echo "Moved $src to $dest"
    else
        echo "Warning: Source file not found, skipping: $src"
    fi
}

echo "Starting refactoring process..."

# --- 1. Create new directories ---
echo "Creating feature directories..."
mkdir -p "$HANDLERS_DIR/account"
mkdir -p "$HANDLERS_DIR/admin"
mkdir -p "$HANDLERS_DIR/invoice"
mkdir -p "$HANDLERS_DIR/payment"
mkdir -p "$HANDLERS_DIR/purchase"
mkdir -p "$HANDLERS_DIR/server"
mkdir -p "$HANDLERS_DIR/user"

mkdir -p "$API_DIR/account"
mkdir -p "$API_DIR/invoice"
mkdir -p "$API_DIR/payment"
mkdir -p "$API_DIR/purchase"
mkdir -p "$API_DIR/server"

# --- 2. Move Handler Files ---
echo "Moving handler files..."

# Account handlers
move_if_exists "$HANDLERS_DIR/account_handler.go" "$HANDLERS_DIR/account/handler.go"
move_if_exists "$HANDLERS_DIR/account_common.go" "$HANDLERS_DIR/account/common.go"
move_if_exists "$HANDLERS_DIR/account_status_handler.go" "$HANDLERS_DIR/account/status_handler.go"
move_if_exists "$HANDLERS_DIR/delete_account_handler.go" "$HANDLERS_DIR/account/delete_handler.go"
move_if_exists "$HANDLERS_DIR/renew_handler.go" "$HANDLERS_DIR/account/renew_handler.go"

# Admin handlers
move_if_exists "$HANDLERS_DIR/admin_settings_handler.go" "$HANDLERS_DIR/admin/settings_handler.go"

# Invoice handler
move_if_exists "$HANDLERS_DIR/invoice_handler.go" "$HANDLERS_DIR/invoice/handler.go"

# Payment handler
move_if_exists "$HANDLERS_DIR/payment_handler.go" "$HANDLERS_DIR/payment/handler.go"

# Purchase handlers
move_if_exists "$HANDLERS_DIR/purchase_hourly_handler.go" "$HANDLERS_DIR/purchase/hourly_handler.go"
move_if_exists "$HANDLERS_DIR/purchase_monthly_handler.go" "$HANDLERS_DIR/purchase/monthly_handler.go"
move_if_exists "$HANDLERS_DIR/purchase_trial_handler.go" "$HANDLERS_DIR/purchase/trial_handler.go"

# Server handler
move_if_exists "$HANDLERS_DIR/server_handler.go" "$HANDLERS_DIR/server/handler.go"

# User handlers
move_if_exists "$HANDLERS_DIR/user_handler.go" "$HANDLERS_DIR/user/handler.go"
move_if_exists "$HANDLERS_DIR/history_handler.go" "$HANDLERS_DIR/user/history_handler.go"


# --- 3. Move API Route Files ---
echo "Moving API route files..."

# Account routes
move_if_exists "$API_DIR/account_routes.go" "$API_DIR/account/routes.go"

# Payment routes
move_if_exists "$API_DIR/payment.go" "$API_DIR/payment/routes.go"

# Purchase routes
move_if_exists "$API_DIR/purchase_routes.go" "$API_DIR/purchase/routes.go"

# Server routes
move_if_exists "$API_DIR/server_routes.go" "$API_DIR/server/routes.go"

echo "File moving complete. Please check the new structure and update package declarations and imports."
