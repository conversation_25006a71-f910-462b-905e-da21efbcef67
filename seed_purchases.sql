-- Hapus data pembelian lama agar tidak duplikat jika skrip dijalankan lagi
DELETE FROM account_trojans;
DELETE FROM account_vmess;
DELETE FROM account_vless;

-- Seeder untuk 10 pembelian akun Trojan
INSERT INTO account_trojans (user_id, kode_server, kode_akun, domain, durasi, username, uuid, expired, tanggal_beli, status, subscription_type, harga, created_at, updated_at)
VALUES
    ('1', 'SGT-01', 'TRJ-FAKE-01', 'sg-test-1.example.com', '30', 'user_trj_01', 'uuid-trj-01', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-02', 'TRJ-FAKE-02', 'sg-test-2.example.com', '30', 'user_trj_02', 'uuid-trj-02', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-01', 'TRJ-FAKE-03', 'sg-test-1.example.com', '30', 'user_trj_03', 'uuid-trj-03', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-03', 'TRJ-FAKE-04', 'sg-test-3.example.com', '30', 'user_trj_04', 'uuid-trj-04', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-02', 'TRJ-FAKE-05', 'sg-test-2.example.com', '30', 'user_trj_05', 'uuid-trj-05', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-01', 'TRJ-FAKE-06', 'sg-test-1.example.com', '30', 'user_trj_06', 'uuid-trj-06', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-01', 'TRJ-FAKE-07', 'sg-test-1.example.com', '30', 'user_trj_07', 'uuid-trj-07', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-03', 'TRJ-FAKE-08', 'sg-test-3.example.com', '30', 'user_trj_08', 'uuid-trj-08', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-02', 'TRJ-FAKE-09', 'sg-test-2.example.com', '30', 'user_trj_09', 'uuid-trj-09', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-01', 'TRJ-FAKE-10', 'sg-test-1.example.com', '30', 'user_trj_10', 'uuid-trj-10', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW());

-- Seeder untuk 10 pembelian akun Vmess
INSERT INTO account_vmess (user_id, kode_server, kode_akun, domain, durasi, username, uuid, expired, tanggal_beli, status, subscription_type, harga, created_at, updated_at)
VALUES
    ('1', 'SGT-02', 'VMS-FAKE-01', 'sg-test-2.example.com', '30', 'user_vms_01', 'uuid-vms-01', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-03', 'VMS-FAKE-02', 'sg-test-3.example.com', '30', 'user_vms_02', 'uuid-vms-02', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-02', 'VMS-FAKE-03', 'sg-test-2.example.com', '30', 'user_vms_03', 'uuid-vms-03', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-01', 'VMS-FAKE-04', 'sg-test-1.example.com', '30', 'user_vms_04', 'uuid-vms-04', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-03', 'VMS-FAKE-05', 'sg-test-3.example.com', '30', 'user_vms_05', 'uuid-vms-05', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-02', 'VMS-FAKE-06', 'sg-test-2.example.com', '30', 'user_vms_06', 'uuid-vms-06', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-02', 'VMS-FAKE-07', 'sg-test-2.example.com', '30', 'user_vms_07', 'uuid-vms-07', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-01', 'VMS-FAKE-08', 'sg-test-1.example.com', '30', 'user_vms_08', 'uuid-vms-08', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-03', 'VMS-FAKE-09', 'sg-test-3.example.com', '30', 'user_vms_09', 'uuid-vms-09', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-02', 'VMS-FAKE-10', 'sg-test-2.example.com', '30', 'user_vms_10', 'uuid-vms-10', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW());

-- Seeder untuk 10 pembelian akun Vless
INSERT INTO account_vless (user_id, kode_server, kode_akun, domain, durasi, username, uuid, expired, tanggal_beli, status, subscription_type, harga, created_at, updated_at)
VALUES
    ('1', 'SGT-03', 'VLS-FAKE-01', 'sg-test-3.example.com', '30', 'user_vls_01', 'uuid-vls-01', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-01', 'VLS-FAKE-02', 'sg-test-1.example.com', '30', 'user_vls_02', 'uuid-vls-02', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-03', 'VLS-FAKE-03', 'sg-test-3.example.com', '30', 'user_vls_03', 'uuid-vls-03', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-02', 'VLS-FAKE-04', 'sg-test-2.example.com', '30', 'user_vls_04', 'uuid-vls-04', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-01', 'VLS-FAKE-05', 'sg-test-1.example.com', '30', 'user_vls_05', 'uuid-vls-05', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-03', 'VLS-FAKE-06', 'sg-test-3.example.com', '30', 'user_vls_06', 'uuid-vls-06', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-03', 'VLS-FAKE-07', 'sg-test-3.example.com', '30', 'user_vls_07', 'uuid-vls-07', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-02', 'VLS-FAKE-08', 'sg-test-2.example.com', '30', 'user_vls_08', 'uuid-vls-08', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-01', 'VLS-FAKE-09', 'sg-test-1.example.com', '30', 'user_vls_09', 'uuid-vls-09', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW()),
    ('1', 'SGT-03', 'VLS-FAKE-10', 'sg-test-3.example.com', '30', 'user_vls_10', 'uuid-vls-10', NOW() + INTERVAL '30 days', NOW(), 'active', 'monthly', '50000', NOW(), NOW());
