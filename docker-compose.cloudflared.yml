version: '3.9'

services:
  cloudflared:
    # Menggunakan image resmi dari Cloudflare
    image: cloudflare/cloudflared:latest
    container_name: cloudflared-tunnel
    restart: unless-stopped
    network_mode: "host"
    # <PERSON><PERSON><PERSON> untuk menjalankan tunnel menggunakan token.
    # Ganti <PASTE_YOUR_TOKEN_HERE> dengan token dari dashboard Cloudflare.
    command: tunnel --no-autoupdate run --token eyJhIjoiN2YyYjc2ZjQ2ZWM0MzQxZWVhYWM2YzJjN2JiY2ExYzAiLCJ0IjoiNDcxNDM4OTAtMTUxMC00NWM4LWFlMzQtN2M3MzVjOWM0MTVkIiwicyI6Ik5XTm1aR0ptTW1NdE0yWXdZUzAwWW1SaExXRTJNemN0TURZMlpUWmxaRGN4WmpSaSJ9

# Catatan:
# Setelah menjalankan 'docker-compose up', <PERSON><PERSON> harus mengkonfigurasi Public Hostname di dashboard Cloudflare.
# Jika menggunakan network_mode: "host", arahkan Public Hostname di dashboard Cloudflare ke:
# URL: http://localhost:8000
