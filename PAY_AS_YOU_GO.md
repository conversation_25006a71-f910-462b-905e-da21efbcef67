# Konse<PERSON>han "Pay As You Go" (<PERSON><PERSON>)

Dokumen ini menjelaskan implementasi sistem penagihan per jam, yang dikenal sebagai "Pay As You Go," dalam aplikasi VPN Shop.

## 1. <PERSON><PERSON><PERSON> <PERSON>ar

"Pay As You Go" adalah model pen<PERSON>han di mana pengguna hanya membayar untuk sumber daya yang benar-benar mereka gunakan. <PERSON>am konteks aplikasi ini, pengguna ditagih per jam untuk setiap akun VPN yang mereka buat dengan tipe langganan per jam.

Alur kerjanya adalah sebagai berikut:
1.  **Pembuatan Akun**: Pengguna membuat akun per jam melalui API. Pada tahap ini, **tidak ada biaya** yang langsung dipotong dari saldo pengguna. Sistem hanya mencatat waktu pembuatan akun (`created_at`).
2.  **Periode Penggunaan**: "Jam" penggunaan mulai berjalan sejak akun berhasil dibuat.
3.  **<PERSON><PERSON>han <PERSON>**: <PERSON><PERSON>han pertama terjadi hanya **setelah satu jam penuh pertama telah berlalu**. <PERSON><PERSON><PERSON>, jika akun dibuat pada pukul 19:05, penagihan pertama untuk 1 jam akan terjadi pada atau setelah pukul 20:05.
4.  **Penagihan Berkelanjutan**: Setelah penagihan pertama, sistem akan terus menagih pengguna untuk setiap jam penuh berikutnya yang telah berlalu sejak waktu penagihan terakhir (`last_billed_at`).
5.  **Ketahanan Terhadap Downtime**: Jika server mengalami *downtime* (misalnya, mati selama 3 jam), saat server kembali online, sistem akan secara otomatis menghitung dan menagih semua jam yang terlewat untuk memastikan tidak ada kerugian pendapatan.

## 2. Mekanisme Keamanan Saldo dan Penonaktifan Otomatis

Untuk mencegah saldo pengguna menjadi negatif dan memastikan kelangsungan layanan, sistem penagihan dilengkapi dengan mekanisme keamanan berlapis yang berjalan untuk **setiap pengguna** secara individual:

1.  **Pengecekan Pra-Penagihan**: Sebelum memotong saldo, sistem akan menghitung total biaya untuk semua jam yang telah digunakan oleh pengguna.
    -   **Jika Saldo Tidak Cukup**: Apabila saldo pengguna tidak mencukupi untuk membayar tagihan yang sudah ada, **semua akun per jam milik pengguna tersebut akan langsung dinonaktifkan**, baik di database lokal maupun di server VPN (via Marzban API). Ini mencegah akumulasi utang lebih lanjut.

2.  **Pengecekan Pasca-Penagihan**: Setelah saldo berhasil dipotong, sistem akan melakukan pengecekan kedua.
    -   **Jika Sisa Saldo Tidak Cukup untuk Jam Berikutnya**: Sistem akan menghitung total biaya untuk *satu jam ke depan* untuk semua akun per jam yang masih aktif. Jika sisa saldo pengguna tidak mencukupi untuk menutupi biaya ini, **semua akun per jamnya juga akan dinonaktifkan**. Ini adalah langkah preventif untuk memastikan pengguna tidak kehabisan saldo secara tiba-tiba di tengah jam penggunaan.

Mekanisme ini memastikan bahwa akun hanya akan tetap aktif jika pengguna memiliki saldo yang cukup untuk membayar penggunaan saat ini dan setidaknya satu jam penggunaan di masa depan.

