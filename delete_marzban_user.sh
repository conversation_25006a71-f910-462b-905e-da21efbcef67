#!/bin/bash

# --- Validasi Argumen ---
if [ -z "$1" ]; then
  echo "Kesalahan: <PERSON><PERSON> pengguna diperlukan."
  echo "Penggunaan: ./delete_marzban_user.sh <username>"
  exit 1
fi

USERNAME=$1

# --- Konfigurasi ---
TOKEN="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiJpbnNvbW5pYSIsImFjY2VzcyI6InN1ZG8iLCJpYXQiOjE3NTEzMDUzMTB9.dTHWIkOQipqC00i2cgJuYiJnfJUhut1GwAt3Wj9oxtU"
API_URL="https://ggl.koneksiku.me/api/user/${USERNAME}"

echo "--- Menghapus Pengguna ---"
echo "Username: ${USERNAME}"
echo "--------------------------"

# --- <PERSON><PERSON> ---
echo "Mengirim permintaan untuk menghapus pengguna..."
HTTP_RESPONSE=$(curl --silent --write-out "HTTPSTATUS:%{http_code}" -X DELETE \
  -H "Authorization: Bearer ${TOKEN}" \
  -H "Accept: application/json" \
  "${API_URL}")

# --- Proses Respons ---
HTTP_BODY=$(echo "$HTTP_RESPONSE" | sed -e 's/HTTPSTATUS:.*//g')
HTTP_STATUS=$(echo "$HTTP_RESPONSE" | tr -d '\n' | sed -e 's/.*HTTPSTATUS://')

echo "Status HTTP: ${HTTP_STATUS}"

if [ "$HTTP_STATUS" -eq 200 ]; then
  echo -e "\nPengguna \033[1;32m'${USERNAME}'\033[0m berhasil dihapus."
else
  echo -e "\n\033[1;31mGagal menghapus pengguna '${USERNAME}'.\033[0m"
  echo "Respons Body: $HTTP_BODY"
fi
