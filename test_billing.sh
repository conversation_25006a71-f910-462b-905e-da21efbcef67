#!/bin/bash

# Script to test the end-to-end hourly billing and account suspension functionality.

set -e

# --- Configuration ---
BACKEND_URL="http://localhost:8080"
DB_CONTAINER_NAME="vpn-shop-db-1" # Use `docker ps` to find the correct name
DB_USER="postgres"
DB_NAME="vpn_shop"

USERNAME="billing_test_user"
PASSWORD="password123"
EMAIL="<EMAIL>"

# --- Helper Functions ---
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] - $1"
}

cleanup() {
    log "Cleaning up test user and accounts..."
    docker exec $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -c "DELETE FROM account_trojans WHERE username LIKE 'trojan_hourly_%';"
    docker exec $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -c "DELETE FROM users WHERE name = '$USERNAME';"
    log "Cleanup complete."
}

# --- Main Test Flow ---

# Trap EXIT signal to run cleanup function
trap cleanup EXIT

log "Starting billing test..."

# 1. Create a new user
log "Creating user '$USERNAME' with an initial balance of 10000..."
curl -s -X POST $BACKEND_URL/api/register \
-H "Content-Type: application/json" \
-d '{
  "name": "'$USERNAME'",
  "email": "'$EMAIL'",
  "password": "'$PASSWORD'",
  "role": "user",
  "saldo": 10000
}' > /dev/null

# 2. Login to get the JWT token
log "Logging in to get JWT token..."
TOKEN=$(curl -s -X POST $BACKEND_URL/api/login \
-H "Content-Type: application/json" \
-d '{
  "email": "'$EMAIL'",
  "password": "'$PASSWORD'"
}' | jq -r '.token')

if [ -z "$TOKEN" ]; then
    log "ERROR: Failed to get JWT token."
    exit 1
fi

# 3. Create an hourly Trojan account (assuming price is 1000/hour)
log "Creating a new hourly Trojan account..."
ACCOUNT_USERNAME="trojan_hourly_$(date +%s)"
curl -s -X POST $BACKEND_URL/api/accounts/trojan \
-H "Content-Type: application/json" \
-H "Authorization: Bearer $TOKEN" \
-d '{
  "is_hourly": true,
  "server_kode": "SGR-1",
  "username": "'$ACCOUNT_USERNAME'"
}' > /dev/null

log "Account '$ACCOUNT_USERNAME' created. Waiting 65 seconds for the first billing cycle..."
sleep 65

# 4. Verify first billing
log "Verifying first billing cycle..."
REMAINING_BALANCE=$(docker exec $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -t -c "SELECT saldo FROM users WHERE name = '$USERNAME';" | xargs)

if [ "$REMAINING_BALANCE" -ge 10000 ]; then
    log "ERROR: Balance was not deducted after the first billing cycle. Balance is still $REMAINING_BALANCE."
    exit 1
fi
log "SUCCESS: First billing successful. Balance is now $REMAINING_BALANCE."

# 5. Set balance to be insufficient for the next hour
log "Setting balance to an insufficient amount (100)..."
docker exec $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -c "UPDATE users SET saldo = 100 WHERE name = '$USERNAME';"

log "Balance updated. Waiting 65 seconds for the next billing cycle to trigger suspension..."
sleep 65

# 6. Verify account suspension
log "Verifying account suspension..."
ACCOUNT_STATUS=$(docker exec $DB_CONTAINER_NAME psql -U $DB_USER -d $DB_NAME -t -c "SELECT status FROM account_trojans WHERE username = '$ACCOUNT_USERNAME';" | xargs)

if [ "$ACCOUNT_STATUS" != "suspended" ]; then
    log "ERROR: Account was not suspended. Current status is '$ACCOUNT_STATUS'."
    exit 1
fi

log "SUCCESS: Account '$ACCOUNT_USERNAME' was correctly suspended due to insufficient balance."
log "Billing test completed successfully!"
