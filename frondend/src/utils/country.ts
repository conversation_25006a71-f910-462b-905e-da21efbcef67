/**
 * Mengon<PERSON>i kode negara dua huruf (ISO 3166-1 alpha-2) menjadi emoji bendera.
 * @param code Kode negara (misal: "ID", "US").
 * @returns String emoji bendera.
 */
const countryNameToCode: { [key: string]: string } = {
  'indonesia': 'ID',
  'singapore': 'SG',
  // Tambahkan pemetaan lain jika diperlukan
};

export function getFlagEmoji(countryIdentifier: string): string {
  let countryCode = countryIdentifier;
  // Cek apakah identifier adalah nama negara yang ada di pemetaan
  if (countryNameToCode[countryIdentifier.toLowerCase()]) {
    countryCode = countryNameToCode[countryIdentifier.toLowerCase()];
  }
    if (!countryCode || countryCode.length !== 2) {
    return '🏳️'; // Bendera netral jika kode tidak valid
  }

  const codePoints = countryCode
    .toUpperCase()
    .split('')
    .map(char => 127397 + char.charCodeAt(0));
  
  return String.fromCodePoint(...codePoints);
}
