@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  .form-card {
    @apply rounded-[10px] bg-white shadow-1 dark:bg-gray-dark dark:shadow-card;
  }

  .form-card-header {
    @apply border-b border-stroke px-4 py-4 font-medium text-dark dark:border-dark-3 dark:text-white sm:px-6 xl:px-7.5;
  }

  .form-card-body {
    @apply p-4 sm:p-6 xl:p-10;
  }

  .form-label {
    @apply mb-3 block text-body-sm font-medium text-dark dark:text-white;
  }

  .form-input {
    @apply w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-5.5 py-3 text-dark placeholder:text-dark-6 outline-none transition focus:border-primary disabled:cursor-default disabled:bg-gray-2 data-[active=true]:border-primary dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary dark:disabled:bg-dark dark:data-[active=true]:border-primary;
  }

  .form-textarea {
    @apply w-full rounded-lg border-[1.5px] border-stroke bg-transparent px-5.5 py-3 text-dark placeholder:text-dark-6 outline-none transition focus:border-primary disabled:cursor-default disabled:bg-gray-2 data-[active=true]:border-primary dark:border-dark-3 dark:bg-dark-2 dark:text-white dark:focus:border-primary dark:disabled:bg-dark dark:data-[active=true]:border-primary;
  }

  .form-select {
    @apply w-full appearance-none rounded-lg border border-stroke bg-transparent px-5.5 py-3 outline-none transition focus:border-primary active:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary;
  }
  
  .form-select-icon-left {
      @apply pl-11.5;
  }

  .form-file-input {
    @apply w-full rounded-lg border-[1.5px] border-stroke bg-transparent outline-none transition focus:border-primary disabled:cursor-default disabled:bg-gray-2 data-[active=true]:border-primary dark:border-dark-3 dark:bg-dark-2 dark:focus:border-primary dark:disabled:bg-dark dark:data-[active=true]:border-primary file:mr-5 file:border-collapse file:cursor-pointer file:border-0 file:border-r file:border-solid file:border-stroke file:bg-[#E2E8F0] file:px-6.5 file:py-[13px] file:text-body-sm file:font-medium file:text-dark-5 file:hover:bg-primary file:hover:bg-opacity-10 dark:file:border-dark-3 dark:file:bg-white/30 dark:file:text-white;
  }

  .form-button-primary {
    @apply flex w-full justify-center rounded-lg bg-primary p-[13px] font-medium text-white hover:bg-opacity-90;
  }

  .form-button-danger {
    @apply flex w-full justify-center rounded-lg bg-[#DC3545] p-[13px] font-medium text-white hover:bg-opacity-90;
  }

  .form-button-secondary {
    @apply flex w-full justify-center rounded-lg bg-gray-2 p-[13px] font-medium text-black hover:bg-gray-3 dark:bg-dark-2 dark:text-white dark:hover:bg-dark-3;
  }
}
