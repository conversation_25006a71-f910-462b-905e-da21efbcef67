"use client";

import { createContext, useContext, useState, useEffect, useCallback, ReactNode } from 'react';
import { useSession } from 'next-auth/react';
import { ApiClient } from '@/lib/apiClientEnhanced';

interface BalanceContextType {
  balance: number | null;
  isLoading: boolean;
  refreshBalance: () => void;
}

const BalanceContext = createContext<BalanceContextType | undefined>(undefined);

export const BalanceProvider = ({ children }: { children: ReactNode }) => {
  const { data: session, status } = useSession();
  const [balance, setBalance] = useState<number | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const fetchBalance = useCallback(async () => {
    if (!session?.accessToken) return;

    setIsLoading(true);
    try {
      const apiClient = new ApiClient();
      const userData = await apiClient.get('/users/me', { token: session.accessToken as string });
      setBalance(userData.saldo);
    } catch (error) {
      console.error("Error saat mengambil saldo dari context:", error);
      const fallbackSaldo = (session.user as any)?.saldo;
      setBalance(fallbackSaldo !== undefined ? fallbackSaldo : null);
    } finally {
      setIsLoading(false);
    }
  }, [session]);

  useEffect(() => {
    // Hanya panggil fetchBalance jika sesi sudah terotentikasi.
    if (status === 'authenticated') {
      fetchBalance();
    }
    const interval = setInterval(fetchBalance, 60000);
    return () => clearInterval(interval);
  }, [status, fetchBalance]);

  return (
    <BalanceContext.Provider value={{ balance, isLoading, refreshBalance: fetchBalance }}>
      {children}
    </BalanceContext.Provider>
  );
};

export const useBalance = () => {
  const context = useContext(BalanceContext);
  if (context === undefined) {
    throw new Error('useBalance must be used within a BalanceProvider');
  }
  return context;
};
