import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { createAuthenticatedApiClient } from "@/lib/apiClientEnhanced";

// Definisikan tipe data yang diharapkan dari API
export interface UserStats {
  total_pay: number;
  jumlah_akun_hourly: number;
  jumlah_akun_month: number;
  total_account: number;
}

/**
 * Mengambil statistik pengguna dari API backend.
 * Fungsi ini harus dijalankan di server-side (misalnya dalam Server Component).
 */
export async function getUserStats(): Promise<UserStats | null> {
  const apiClient = await createAuthenticatedApiClient();

  if (!apiClient) {
    console.error("No access token found in session");
    return null;
  }

  try {
    const data = await apiClient.get('/users/me/stats');

    // Pilih hanya data yang dibutuhkan
    const selectedData: UserStats = {
      total_pay: data.total_pay || 0,
      jumlah_akun_hourly: data.jumlah_akun_hourly || 0,
      jumlah_akun_month: data.jumlah_akun_month || 0,
      total_account: data.total_account || 0,
    };

    return selectedData;

  } catch (error) {
    console.error("An unexpected error occurred while fetching user stats:", error);
    return null;
  }
}
