'use client';

import { useState } from 'react';
import { Copy, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface CopyButtonProps {
  text: string;
  className?: string;
  iconSize?: number;
}

export const CopyButton = ({ text, className, iconSize = 16 }: CopyButtonProps) => {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <button
      onClick={handleCopy}
      className={cn(
        'inline-flex items-center justify-center rounded-full p-1 transition-colors hover:bg-gray-100 dark:hover:bg-gray-800',
        className
      )}
      title={copied ? 'Disalin!' : 'Salin ke clipboard'}
    >
      {copied ? (
        <Check size={iconSize} className="text-green-500" />
      ) : (
        <Copy size={iconSize} className="text-gray-500 dark:text-gray-400" />
      )}
    </button>
  );
};