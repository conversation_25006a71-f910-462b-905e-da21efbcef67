"use client";

import { useState, useCallback } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { TransactionApiResponse, TransactionType, TransactionStatus, getTransactionTypeLabel, getTransactionStatusLabel } from '@/types/transaction';
import dayjs from 'dayjs';
import { cn } from '@/lib/utils';
import { Eye, Info } from 'lucide-react';
import Popover from '@/components/ui/popover';
import { debounce } from 'lodash';

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';



interface TransactionTableProps {
  initialData: TransactionApiResponse;
}

const TransactionTable = ({ initialData }: TransactionTableProps) => {
  const router = useRouter();
  const searchParams = useSearchParams();

  const transactions = initialData.data || [];
  const currentPage = initialData.pagination?.current_page || 1;
  const totalPages = initialData.pagination?.last_page || 1;

  const [searchQuery, setSearchQuery] = useState(searchParams.get('search') || "");
  const [typeFilter, setTypeFilter] = useState<TransactionType | "">(
    (searchParams.get('type') as TransactionType) || ""
  );
  const [statusFilter, setStatusFilter] = useState<TransactionStatus | "">(
    (searchParams.get('status') as TransactionStatus) || ""
  );

  const debouncedNavigate = useCallback(debounce((query: string, type: string, status: string, page: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set('page', String(page));
    if (query) {
      params.set('search', query);
    } else {
      params.delete('search');
    }
    if (type) {
      params.set('type', type);
    } else {
      params.delete('type');
    }
    if (status) {
      params.set('status', status);
    } else {
      params.delete('status');
    }
    router.replace(`/transactions?${params.toString()}`);
  }, 500), [router, searchParams]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchQuery(value);
    debouncedNavigate(value, typeFilter, statusFilter, 1);
  };

  const handleTypeFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value as TransactionType | "";
    setTypeFilter(value);
    debouncedNavigate(searchQuery, value, statusFilter, 1);
  };

  const handleStatusFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const value = e.target.value as TransactionStatus | "";
    setStatusFilter(value);
    debouncedNavigate(searchQuery, typeFilter, value, 1);
  };

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      debouncedNavigate(searchQuery, typeFilter, statusFilter, page);
    }
  };

  const getPaginationLinks = () => {
    const links = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    // Previous button
    links.push(
      <li key="prev">
        <button
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
          className={cn(
            "flex h-9 w-9 items-center justify-center rounded-md border border-stroke text-dark hover:border-primary hover:bg-primary hover:text-white dark:border-dark-3 dark:text-white dark:hover:border-primary dark:hover:bg-primary",
            currentPage === 1 && "cursor-not-allowed opacity-50"
          )}
        >
          &lt;
        </button>
      </li>
    );

    // Page numbers
    for (let i = startPage; i <= endPage; i++) {
      links.push(
        <li key={i}>
          <button
            onClick={() => handlePageChange(i)}
            className={cn(
              "flex h-9 w-9 items-center justify-center rounded-md border text-dark hover:border-primary hover:bg-primary hover:text-white dark:text-white dark:hover:border-primary dark:hover:bg-primary",
              i === currentPage
                ? "border-primary bg-primary text-white"
                : "border-stroke dark:border-dark-3"
            )}
          >
            {i}
          </button>
        </li>
      );
    }

    // Next button
    links.push(
      <li key="next">
        <button
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
          className={cn(
            "flex h-9 w-9 items-center justify-center rounded-md border border-stroke text-dark hover:border-primary hover:bg-primary hover:text-white dark:border-dark-3 dark:text-white dark:hover:border-primary dark:hover:bg-primary",
            currentPage === totalPages && "cursor-not-allowed opacity-50"
          )}
        >
          &gt;
        </button>
      </li>
    );

    return links;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('id-ID', {
      style: 'currency',
      currency: 'IDR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="rounded-[10px] border border-stroke bg-white p-4 shadow-1 dark:border-dark-3 dark:bg-gray-dark dark:shadow-card sm:p-7.5">
      <div className="flex flex-col gap-4 mb-6 sm:flex-row sm:items-center sm:justify-between">
        <h4 className="text-xl font-semibold text-black dark:text-white">
          Riwayat Transaksi
        </h4>
        
        <div className="flex flex-col gap-3 sm:flex-row sm:items-center">
          {/* Search Input */}
          <div className="relative">
            <input
              type="text"
              placeholder="Cari transaksi..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="flex items-center w-full max-w-xs gap-3.5 rounded-full border bg-gray-2 p-3 pl-10 outline-none ring-primary transition-colors focus-visible:ring-1 dark:border-dark-3 dark:bg-dark-2 dark:hover:border-dark-4 dark:hover:bg-dark-3 dark:hover:text-dark-6"
            />
            <div className="absolute top-1/2 left-3 -translate-y-1/2">
              <svg width="18" height="18" viewBox="0 0 18 18" fill="currentColor" className="max-[1015px]:size-5">
                <g clipPath="url(#clip0_1699_11536)">
                  <path fillRule="evenodd" clipRule="evenodd" d="M8.625 2.0625C5.00063 2.0625 2.0625 5.00063 2.0625 8.625C2.0625 12.2494 5.00063 15.1875 8.625 15.1875C12.2494 15.1875 15.1875 12.2494 15.1875 8.625C15.1875 5.00063 12.2494 2.0625 8.625 2.0625ZM0.9375 8.625C0.9375 4.37931 4.37931 0.9375 8.625 0.9375C12.8707 0.9375 16.3125 4.37931 16.3125 8.625C16.3125 10.5454 15.6083 12.3013 14.4441 13.6487L16.8977 16.1023C17.1174 16.3219 17.1174 16.6781 16.8977 16.8977C16.6781 17.1174 16.3219 17.1174 16.1023 16.8977L13.6487 14.4441C12.3013 15.6083 10.5454 16.3125 8.625 16.3125C4.37931 16.3125 0.9375 12.8707 0.9375 8.625Z"></path>
                </g>
                <defs>
                  <clipPath id="clip0_1699_11536">
                    <rect width="18" height="18" fill="white"></rect>
                  </clipPath>
                </defs>
              </svg>
            </div>
          </div>

          {/* Type Filter */}
          <select
            value={typeFilter}
            onChange={handleTypeFilterChange}
            className="rounded-md border border-stroke bg-white px-3 py-2 text-dark outline-none ring-primary transition-colors focus-visible:ring-1 dark:border-dark-3 dark:bg-dark-2 dark:text-white"
          >
            <option value="">Semua Jenis</option>
            <option value="TOPUP">Top Up</option>
            <option value="PURCHASE_MONTHLY">Pembelian Bulanan</option>
            <option value="PURCHASE_HOURLY">Pembelian Per Jam</option>
            <option value="TRIAL">Trial</option>
            <option value="RENEWAL">Perpanjangan</option>
            <option value="REFUND">Refund</option>
            <option value="BILLING">Billing</option>
            <option value="BILLED_HOURLY">Billing Per Jam</option>
          </select>

          {/* Status Filter */}
          <select
            value={statusFilter}
            onChange={handleStatusFilterChange}
            className="rounded-md border border-stroke bg-white px-3 py-2 text-dark outline-none ring-primary transition-colors focus-visible:ring-1 dark:border-dark-3 dark:bg-dark-2 dark:text-white"
          >
            <option value="">Semua Status</option>
            <option value="PENDING">Menunggu</option>
            <option value="SUCCESS">Berhasil</option>
            <option value="FAILED">Gagal</option>
            <option value="EXPIRED">Kedaluwarsa</option>
          </select>
        </div>
      </div>

      <div className="max-w-full overflow-x-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
        <Table>
          <TableHeader>
            <TableRow className="border-none bg-[#F7F9FC] dark:bg-dark-2 [&>th]:py-4 [&>th]:text-base [&>th]:text-dark [&>th]:dark:text-white">
              <TableHead className="min-w-[200px] xl:pl-7.5">Invoice ID</TableHead>
              <TableHead className="min-w-[200px]">Deskripsi</TableHead>
              <TableHead className="min-w-[180px]">Jenis</TableHead>
              <TableHead className="min-w-[120px]">Jumlah</TableHead>
              <TableHead className="min-w-[100px]">Status</TableHead>
              <TableHead className="min-w-[120px]">Tanggal</TableHead>
              <TableHead className="text-center xl:pr-7.5 min-w-[80px]">Aksi</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {transactions.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8">
                  <p className="text-gray-500 dark:text-gray-400">Tidak ada transaksi ditemukan</p>
                </TableCell>
              </TableRow>
            ) : (
              transactions.map((transaction) => (
                <TableRow key={transaction.id} className="border-[#eee] dark:border-dark-3">
                  <TableCell className="xl:pl-7.5">
                    <p className="font-medium text-black dark:text-white break-all">
                      {transaction.invoice_id}
                    </p>
                  </TableCell>

                  <TableCell>
                    <Popover
                      position="bottom"
                      trigger={
                        <div className="flex items-center gap-2 cursor-pointer hover:text-primary transition-colors group">
                          <p className="text-black dark:text-white truncate max-w-[180px] group-hover:text-primary">
                            {transaction.description}
                          </p>
                          <Info size={14} className="text-gray-400 dark:text-gray-500 group-hover:text-primary flex-shrink-0" />
                        </div>
                      }
                      content={
                        <div>
                          <h4 className="font-semibold text-black dark:text-white mb-2">
                            Detail Transaksi
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-300 leading-relaxed">
                            {transaction.description}
                          </p>
                          <div className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-600">
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              Invoice: {transaction.invoice_id}
                            </p>
                          </div>
                        </div>
                      }
                    />
                  </TableCell>

                  <TableCell>
                    <p className="text-black dark:text-white font-medium">
                      {getTransactionTypeLabel(transaction.type)}
                    </p>
                  </TableCell>

                  <TableCell>
                    <p className="font-medium text-black dark:text-white">
                      {formatCurrency(transaction.amount)}
                    </p>
                  </TableCell>

                  <TableCell>
                    <p
                      className={cn(
                        "inline-flex rounded-full bg-opacity-10 px-3 py-1 text-sm font-medium",
                        {
                          "bg-yellow-dark text-yellow-dark": transaction.status === 'PENDING',
                          "bg-green text-green": transaction.status === 'SUCCESS',
                          "bg-red text-red": transaction.status === 'FAILED',
                          "bg-gray-500 text-gray-500": transaction.status === 'EXPIRED',
                        }
                      )}
                    >
                      {getTransactionStatusLabel(transaction.status)}
                    </p>
                  </TableCell>

                  <TableCell>
                    <p className="text-black dark:text-white">
                      {dayjs(transaction.created_at).format('DD MMM YYYY')}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {dayjs(transaction.created_at).format('HH:mm')}
                    </p>
                  </TableCell>

                  <TableCell className="xl:pr-7.5">
                    <div className="flex items-center justify-center">
                      <button
                        onClick={() => router.push(`/product/order/invoice?invoice_id=${transaction.invoice_id}`)}
                        className="hover:text-primary"
                        title="Lihat Invoice"
                      >
                        <Eye size={20} />
                      </button>
                    </div>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="p-4 sm:p-6 xl:p-7.5">
          <nav>
            <ul className="flex flex-wrap items-center gap-1">
              {getPaginationLinks()}
            </ul>
          </nav>
        </div>
      )}
    </div>
  );
};

export default TransactionTable;
