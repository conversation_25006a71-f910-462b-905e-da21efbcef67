/* eslint-disable @next/next/no-sync-scripts */
'use client';

import { useEffect } from 'react';
import { signIn } from 'next-auth/react';
import { useNotification } from '@/contexts/NotificationContext';
import { useRouter } from 'next/navigation';

// Definisikan tipe untuk data pengguna dari Telegram
interface TelegramUser {
  id: number;
  first_name: string;
  last_name?: string;
  username?: string;
  photo_url?: string;
  auth_date: number;
  hash: string;
}

// Definisikan tipe untuk window agar bisa menampung callback
declare global {
  interface Window {
    onTelegramAuth: (user: TelegramUser) => void;
  }
}

const TelegramLoginWidget = () => {
  const { addNotification } = useNotification();
  const router = useRouter();

  useEffect(() => {
    // Fungsi ini akan dipanggil oleh Telegram setelah otentikasi berhasil
    window.onTelegramAuth = async (user: TelegramUser) => {
      const result = await signIn('telegram', {
        redirect: false,
        // <PERSON><PERSON> seluruh objek user sebagai string JSON dalam satu field
        user_data: JSON.stringify(user),
      });

      if (result?.ok) {
        addNotification('Login berhasil! Mengarahkan ke dashboard...', 'success');
        router.push('/dashboard');
      } else {
        addNotification(result?.error || 'Login gagal. Silakan coba lagi.', 'error');
      }
    };

    const script = document.createElement('script');
    script.src = 'https://telegram.org/js/telegram-widget.js?22';
    script.async = true;
    script.setAttribute('data-telegram-login', 'golang_restapi_bot'); // Ganti dengan username bot Anda
    script.setAttribute('data-size', 'large');
    script.setAttribute('data-onauth', 'onTelegramAuth(user)');
    script.setAttribute('data-request-access', 'write');

    const widgetContainer = document.getElementById('telegram-login-widget');
    if (widgetContainer) {
      widgetContainer.appendChild(script);
    }

    return () => {
      // Cleanup script jika komponen di-unmount
      if (widgetContainer && widgetContainer.contains(script)) {
        widgetContainer.removeChild(script);
      }
    };
  }, []);

  return <div id="telegram-login-widget"></div>;
};

export default TelegramLoginWidget;
