'use client';

import { useState } from 'react';
import { ChevronUpIcon } from '@/assets/icons';
import { cn } from '@/lib/utils';

interface AccordionItemProps {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
}

interface AccordionProps {
  children: React.ReactNode;
  className?: string;
}

export function AccordionItem({ title, children, defaultOpen = false }: AccordionItemProps) {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className="border-b border-gray-200 dark:border-gray-700 last:border-b-0">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex w-full items-center justify-between py-4 text-left font-medium text-gray-900 dark:text-white hover:text-primary dark:hover:text-primary transition-colors"
      >
        <span>{title}</span>
        <ChevronUpIcon
          className={cn(
            "h-5 w-5 transition-transform duration-200",
            isOpen ? "" : "rotate-180"
          )}
        />
      </button>
      <div
        className={cn(
          "overflow-hidden transition-all duration-200 ease-in-out",
          isOpen ? "max-h-96 pb-4" : "max-h-0"
        )}
      >
        <div className="space-y-3">
          {children}
        </div>
      </div>
    </div>
  );
}

export function Accordion({ children, className }: AccordionProps) {
  return (
    <div className={cn("space-y-0", className)}>
      {children}
    </div>
  );
}