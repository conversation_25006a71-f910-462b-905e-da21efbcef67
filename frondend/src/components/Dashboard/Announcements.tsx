'use client';

import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { BellIcon } from '@/assets/icons'; // Placeholder icon

// Definisikan tipe data untuk pengumuman
interface Announcement {
  id: number;
  judul: string;
  isi: string;
  created_at: string;
}

// Fungsi untuk mengambil data pengumuman
async function getAnnouncements(): Promise<Announcement[]> {
  try {
    const apiUrl = process.env.NEXT_PUBLIC_API_URL;
    const response = await fetch(`${apiUrl}/public/announcements`);
    if (!response.ok) {
      console.error('Gagal mengambil data pengumuman, status:', response.status);
      return [];
    }
    const data = await response.json();
    return data || [];
  } catch (error) {
    console.error('Error saat fetching announcements:', error);
    return []; // Kembalikan array kosong jika terjadi error
  }
}

export function Announcements({ className }: { className?: string }) {
  const [announcements, setAnnouncements] = useState<Announcement[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    async function loadAnnouncements() {
      setLoading(true);
      const data = await getAnnouncements();
      setAnnouncements(data);
      setLoading(false);
    }
    loadAnnouncements();
  }, []);

  return (
    <div
      className={cn(
        "grid gap-4 rounded-xl bg-white p-6 shadow-1 dark:bg-gray-dark dark:shadow-card",
        className,
      )}
    >
      <div className="flex items-center gap-2">
        <BellIcon className="h-6 w-6 text-dark dark:text-white" />
        <h2 className="text-body-2xlg font-bold text-dark dark:text-white">
          Pengumuman
        </h2>
      </div>

      <div className="h-[350px] overflow-y-auto pr-4 custom-scrollbar">
        {loading ? (
          <div className="flex h-full items-center justify-center">
            <div className="h-10 w-10 animate-spin rounded-full border-4 border-solid border-primary border-t-transparent"></div>
          </div>
        ) : announcements.length > 0 ? (
          <div className="relative border-l-2 border-dashed border-gray-200 dark:border-dark-3 ml-3">
            <ul className="space-y-8">
              {announcements.map((item) => (
                <li key={item.id} className="relative pl-8">
                  <div className="absolute -left-[11px] top-1 flex h-5 w-5 items-center justify-center rounded-full bg-primary">
                    <div className="h-2 w-2 rounded-full bg-white"></div>
                  </div>
                  <div className="flex flex-col">
                    <div className="flex items-baseline gap-2">
                      <h4 className="font-semibold text-dark dark:text-white">{item.judul}</h4>
                    </div>
                    <p className="mt-1 text-sm text-gray-600 dark:text-dark-6">{item.isi}</p>
                    <p className="mt-2 text-xs text-gray-400 dark:text-dark-6">
                      {new Date(item.created_at).toLocaleDateString('id-ID', {
                        day: 'numeric', month: 'long', year: 'numeric'
                      })}
                    </p>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        ) : (
          <div className="flex h-full flex-col items-center justify-center text-center text-gray-500 dark:text-dark-6">
            <BellIcon className="h-12 w-12 text-gray-300 dark:text-dark-4" />
            <p className="mt-4 text-lg font-medium">Tidak Ada Pengumuman</p>
            <p className="text-sm">Semua informasi terbaru akan ditampilkan di sini.</p>
          </div>
        )}
      </div>
    </div>
  );
}
