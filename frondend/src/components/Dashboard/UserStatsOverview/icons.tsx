import type { JSX, SVGProps } from "react";

// Ikon untuk Total Pembayaran (Rupiah)
export function TotalPayIcon(props: SVGProps<SVGSVGElement>): JSX.Element {
  return (
    <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-blue">
      <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2} {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8v1m0 6v1m-6-3h12" />
      </svg>
    </div>
  );
}

// Ikon untuk Akun Per Jam (Jam)
export function HourlyIcon(props: SVGProps<SVGSVGElement>): JSX.Element {
  return (
    <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-green">
      <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2} {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
    </div>
  );
}

// Ikon untuk Akun Bulanan (Kalender)
export function MonthlyIcon(props: SVGProps<SVGSVGElement>): JSX.Element {
  return (
    <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-orange-light">
      <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2} {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    </div>
  );
}

// Ikon untuk Total Akun (Pengguna)
export function TotalAccountIcon(props: SVGProps<SVGSVGElement>): JSX.Element {
  return (
    <div className="flex h-11.5 w-11.5 items-center justify-center rounded-full bg-primary">
      <svg className="h-6 w-6 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2} {...props}>
        <path strokeLinecap="round" strokeLinejoin="round" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
    </div>
  );
}
