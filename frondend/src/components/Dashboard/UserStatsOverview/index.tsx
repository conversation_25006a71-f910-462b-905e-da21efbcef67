import { getUserStats, UserStats } from "@/services/user.services";
import { Suspense } from "react";
import { StatCard } from "./card";
import { HourlyIcon, MonthlyIcon, TotalAccountIcon, TotalPayIcon } from "./icons";

// Komponen Skeleton untuk ditampilkan saat data sedang dimuat
const UserStatsSkeleton = () => (
  <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:gap-6 xl:grid-cols-4">
    {[...Array(4)].map((_, i) => (
      <div key={i} className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark h-[124px] animate-pulse"></div>
    ))}
  </div>
);

async function StatsDisplay() {
  const stats: UserStats | null = await getUserStats();

  if (!stats) {
    return (
      <div className="col-span-full rounded-lg border border-danger bg-danger/10 p-4 text-center text-danger">
        Gagal memuat statistik pengguna.
      </div>
    );
  }

  return (
    <>
      <StatCard 
        label="Total Pembayaran" 
        value={stats.total_pay} 
        currency="Rp" 
        Icon={TotalPayIcon} 
      />
      <StatCard 
        label="Akun Per Jam" 
        value={stats.jumlah_akun_hourly} 
        Icon={HourlyIcon} 
      />
      <StatCard 
        label="Akun Bulanan" 
        value={stats.jumlah_akun_month} 
        Icon={MonthlyIcon} 
      />
      <StatCard 
        label="Total Akun" 
        value={stats.total_account} 
        Icon={TotalAccountIcon} 
      />
    </>
  );
}

export function UserStatsOverview() {
  return (
    <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 md:gap-6 xl:grid-cols-4">
      <Suspense fallback={<UserStatsSkeleton />}>
        <StatsDisplay />
      </Suspense>
    </div>
  );
}
