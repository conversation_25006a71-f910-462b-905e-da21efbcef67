import React from 'react';

interface InsomVPNLogoProps {
  className?: string;
  variant?: 'full' | 'icon-only' | 'text-only';
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

export const InsomVPNLogo: React.FC<InsomVPNLogoProps> = ({ 
  className = '', 
  variant = 'full',
  size = 'md'
}) => {
  const sizeClasses = {
    sm: 'h-6',
    md: 'h-8',
    lg: 'h-10',
    xl: 'h-12'
  };

  const textSizes = {
    sm: 'text-lg',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-3xl'
  };

  // Logo Icon Component
  const LogoIcon = ({ iconSize }: { iconSize: string }) => (
    <svg
      className={iconSize}
      viewBox="0 0 48 48"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      {/* Shield Background */}
      <path
        d="M24 4L8 10V22C8 32 16 40.5 24 44C32 40.5 40 32 40 22V10L24 4Z"
        fill="url(#shieldGradient)"
        stroke="currentColor"
        strokeWidth="1.5"
        strokeLinejoin="round"
      />
      
      {/* Lock Body */}
      <rect
        x="16"
        y="22"
        width="16"
        height="12"
        rx="2"
        fill="currentColor"
        fillOpacity="0.9"
      />
      
      {/* Lock Shackle */}
      <path
        d="M20 22V18C20 15.7909 21.7909 14 24 14C26.2091 14 28 15.7909 28 18V22"
        stroke="currentColor"
        strokeWidth="2.5"
        strokeLinecap="round"
        fill="none"
      />
      
      {/* Lock Keyhole */}
      <circle
        cx="24"
        cy="27"
        r="2"
        fill="white"
      />
      <rect
        x="23"
        y="27"
        width="2"
        height="4"
        fill="white"
      />
      
      {/* VPN Connection Lines */}
      <path
        d="M12 16L16 18M32 18L36 16"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeOpacity="0.7"
      />
      
      {/* Gradient Definitions */}
      <defs>
        <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3B82F6" stopOpacity="0.2" />
          <stop offset="50%" stopColor="#1D4ED8" stopOpacity="0.3" />
          <stop offset="100%" stopColor="#1E40AF" stopOpacity="0.4" />
        </linearGradient>
      </defs>
    </svg>
  );

  if (variant === 'icon-only') {
    return (
      <div className={`flex items-center ${className}`}>
        <LogoIcon iconSize={sizeClasses[size]} />
      </div>
    );
  }

  if (variant === 'text-only') {
    return (
      <div className={`flex items-center ${className}`}>
        <span className={`font-bold text-primary ${textSizes[size]}`}>
          InsomVPN
        </span>
      </div>
    );
  }

  return (
    <div className={`flex items-center gap-2 ${className}`}>
      <LogoIcon iconSize={sizeClasses[size]} />
      <span className={`font-bold text-dark dark:text-white ${textSizes[size]}`}>
        Insom<span className="text-primary">VPN</span>
      </span>
    </div>
  );
};

// Alias untuk backward compatibility
export const Logo = InsomVPNLogo;
export default InsomVPNLogo;
