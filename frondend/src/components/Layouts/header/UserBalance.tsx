"use client";

import { useBalance } from '@/contexts/BalanceContext';
import { WalletIcon } from "./icons";

const UserBalance = () => {
  const { balance, isLoading } = useBalance();

  return (
    <div className="flex items-center gap-2 rounded-full bg-gray-2 px-4 py-2 dark:bg-dark-2">
      <WalletIcon className="h-6 w-6 text-primary" />
      <span className="font-bold text-dark dark:text-white">
        {isLoading
          ? "Memuat..."
          : balance !== null
          ? new Intl.NumberFormat('id-ID', { style: 'currency', currency: 'IDR', minimumFractionDigits: 0, maximumFractionDigits: 0 }).format(balance)
          : "Rp 0"}
      </span>
    </div>
  );
};

export default UserBalance;
