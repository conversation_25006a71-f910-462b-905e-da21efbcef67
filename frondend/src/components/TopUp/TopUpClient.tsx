'use client';

import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { ApiClient } from '@/lib/apiClientEnhanced';
import InputGroup from '@/components/FormElements/InputGroup';
import { Button } from '@/components/ui-elements/button';

// Definisikan tipe untuk channel pembayaran
interface IPaymentChannel {
  code: string;
  name: string;
  icon_url: string;
  group: string;
  fee_customer: {
    flat: number;
    percent: number;
  };
}

export default function TopUpClient() {
  const { data: session } = useSession();

  // State untuk data top up
  const [amount, setAmount] = useState('');
  const [selectedMethod, setSelectedMethod] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State untuk mengambil payment channels
  const [paymentChannels, setPaymentChannels] = useState<IPaymentChannel[]>([]);
  const [isLoadingChannels, setIsLoadingChannels] = useState(true);
  const [channelsError, setChannelsError] = useState<string | null>(null);

  // useEffect untuk mengambil data payment channels dari API
  useEffect(() => {
    const fetchPaymentChannels = async () => {
      if (!session?.accessToken) {
        setIsLoadingChannels(false);
        return; // Jangan fetch jika tidak ada token
      }

      setIsLoadingChannels(true);
      setChannelsError(null);

      try {
        const apiClient = new ApiClient();
        const response = await apiClient.get('/payment/channels', {
          token: session.accessToken,
        });
        setPaymentChannels(response.data || []);
      } catch (err) {
        setChannelsError(err instanceof Error ? err.message : 'Terjadi kesalahan tidak diketahui.');
      } finally {
        setIsLoadingChannels(false);
      }
    };

    fetchPaymentChannels();
  }, [session?.accessToken]); // Hanya jalankan ulang jika accessToken berubah, bukan seluruh objek sesi

  const handleTopUp = async () => {
    if (!amount || !selectedMethod || !session?.accessToken) {
      setError('Pastikan jumlah dan metode pembayaran telah diisi dan Anda telah login.');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      const apiClient = new ApiClient();
      const data = await apiClient.post('/payment/topup', {
        amount: parseInt(amount, 10),
        payment_method: selectedMethod,
      }, {
        token: session.accessToken,
      });

      if (data.checkout_url) {
        window.location.href = data.checkout_url;
      } else {
        throw new Error('URL Checkout tidak ditemukan dalam respons.');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Terjadi kesalahan tidak diketahui.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatFee = (fee: { flat: number; percent: number }): string => {
    const flatFee = fee.flat > 0 ? `Rp ${fee.flat.toLocaleString('id-ID')}` : '';
    const percentFee = fee.percent > 0 ? `${fee.percent}%` : '';

    if (flatFee && percentFee) {
      return `${flatFee} + ${percentFee}`;
    }
    if (flatFee) {
      return flatFee;
    }
    if (percentFee) {
      return percentFee;
    }
    return 'Gratis';
  };

  const renderPaymentChannels = () => {
    if (isLoadingChannels) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="flex items-center gap-3">
            <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-500 border-t-transparent"></div>
            <p className="text-gray-600 dark:text-gray-400 font-medium">Memuat metode pembayaran...</p>
          </div>
        </div>
      );
    }

    if (channelsError) {
      return (
        <div className="relative overflow-hidden bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-xl border border-red-200/50 dark:border-red-700/50 p-4 text-center">
          <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 to-pink-500/5"></div>
          <div className="relative flex items-center justify-center gap-2">
            <svg className="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
            <span className="text-sm font-medium text-red-800 dark:text-red-300">
              <span className="font-semibold">Gagal memuat:</span> {channelsError}
            </span>
          </div>
        </div>
      );
    }

    if (paymentChannels.length === 0) {
      return (
        <div className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="p-4 bg-gray-100 dark:bg-gray-700 rounded-2xl mb-4 inline-block">
              <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
            </div>
            <p className="text-gray-600 dark:text-gray-400 font-medium">Tidak ada metode pembayaran yang tersedia saat ini</p>
          </div>
        </div>
      );
    }

    return (
      <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-4">
        {paymentChannels.map((method) => (
          <div
            key={method.code}
            onClick={() => !isSubmitting && setSelectedMethod(method.code)}
            className={`group relative overflow-hidden cursor-pointer rounded-2xl border-2 transition-all duration-300 hover:scale-105 hover:shadow-lg ${
              selectedMethod === method.code
                ? 'border-blue-500 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/30 dark:to-indigo-900/30 shadow-lg'
                : 'border-gray-200 dark:border-gray-600 bg-white/80 dark:bg-gray-700/80 hover:border-blue-300 dark:hover:border-blue-500'
            }`}
          >
            {/* Selection indicator */}
            {selectedMethod === method.code && (
              <div className="absolute top-2 right-2 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              </div>
            )}
            
            <div className="p-4 flex flex-col items-center justify-center gap-3 h-full min-h-[120px]">
              <div className="relative">
                <img 
                  src={method.icon_url} 
                  alt={method.name} 
                  className="h-10 w-auto object-contain transition-transform duration-300 group-hover:scale-110" 
                />
              </div>
              
              <div className="text-center space-y-1">
                <span className="text-sm font-semibold text-gray-900 dark:text-white block leading-tight">
                  {method.name}
                </span>
                <span className="text-xs font-medium text-gray-600 dark:text-gray-400 block">
                  Fee: {formatFee(method.fee_customer)}
                </span>
              </div>
            </div>
            
            {/* Hover effect overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </div>
        ))}
      </div>
    );
  };

  return (
    <div className="relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-xl border border-blue-200/50 dark:border-gray-700">
      {/* Background decorations */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5"></div>
      <div className="absolute top-0 right-0 w-32 h-32 bg-blue-400/10 rounded-full -translate-y-16 translate-x-16"></div>
      <div className="absolute bottom-0 left-0 w-24 h-24 bg-purple-400/10 rounded-full translate-y-12 -translate-x-12"></div>
      
      <div className="relative p-6 sm:p-8">
        {/* Header Section */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <div className="p-4 bg-blue-500/10 rounded-2xl">
              <svg className="w-8 h-8 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div>
              <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 dark:text-white">Top Up Saldo</h2>
              <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">Isi saldo akun Anda untuk membeli atau memperpanjang layanan</p>
            </div>
          </div>
        </div>

        <form onSubmit={(e) => { e.preventDefault(); handleTopUp(); }}>
          <div className="space-y-6">
            {/* Error Alert */}
            {error && (
              <div className="relative overflow-hidden bg-gradient-to-r from-red-50 to-pink-50 dark:from-red-900/20 dark:to-pink-900/20 rounded-2xl border border-red-200/50 dark:border-red-700/50 p-4 sm:p-6">
                <div className="absolute inset-0 bg-gradient-to-r from-red-500/5 to-pink-500/5"></div>
                <div className="relative flex items-center gap-3">
                  <div className="p-2 bg-red-500/10 rounded-xl">
                    <svg className="w-5 h-5 text-red-600 dark:text-red-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <div>
                    <p className="text-sm font-medium text-red-800 dark:text-red-300">
                      <span className="font-semibold">Error!</span> {error}
                    </p>
                  </div>
                </div>
              </div>
            )}

            {/* Amount Input */}
            <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-600/20 p-4 sm:p-6 shadow-lg">
              <InputGroup
                label="Jumlah Top Up (IDR)"
                type="number"
                placeholder="Masukkan jumlah, contoh: 50000"
                value={amount}
                handleChange={(e: React.ChangeEvent<HTMLInputElement>) => setAmount(e.target.value)}
                disabled={isSubmitting || isLoadingChannels}
              />
            </div>

            {/* Payment Methods */}
            <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-600/20 p-4 sm:p-6 shadow-lg">
              <div className="flex items-center gap-3 mb-6">
                <div className="p-3 bg-purple-500/10 rounded-xl">
                  <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">Pilih Metode Pembayaran</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Pilih metode pembayaran yang Anda inginkan</p>
                </div>
              </div>
              {renderPaymentChannels()}
            </div>

            {/* Submit Button */}
            <div className="flex justify-end pt-2">
              <Button
                label={isSubmitting ? '⏳ Memproses...' : '🚀 Lanjutkan Pembayaran'}
                variant="primary"
                type="submit"
                disabled={!amount || !selectedMethod || isSubmitting || isLoadingChannels}
                className="px-8 py-4 font-semibold text-lg bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white rounded-2xl shadow-xl transition-all duration-300 hover:shadow-2xl hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
              />
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}
