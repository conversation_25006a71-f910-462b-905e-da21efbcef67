import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"
import { format } from "date-fns"
import { id } from "date-fns/locale"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(date: Date): string {
  return format(date, 'dd MMMM yyyy', { locale: id })
}

export function formatDateTime(date: Date): string {
  return format(date, 'dd MMMM yyyy HH:mm', { locale: id })
}
