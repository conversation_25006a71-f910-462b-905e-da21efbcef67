export interface Role {
  ID: number;
  Name: string;
  Description?: string;
}

export interface UserProfile {
  id: number;
  username: string;
  name: string;
  email: string;
  whatsapp: string;
  profilePhoto: string;
  emailVerified: boolean;
  whatsappVerified: boolean;
  accountType: string;
  emailVerifiedAt: string | null;
  saldo: number;
  roles: Role[];
  pay_bulanan: number;
  total_pay: number;
  batas_trial: number;
  trial: number;
}
