export interface PublicTransaction {
  id: number;
  invoice_id: string;
  user_name: string;
  user_avatar?: string;
  description: string;
  amount: number;
  created_at: string;
  status?: string;
}

export type TransactionType =
  | "TOPUP"
  | "PURCHASE_MONTHLY"
  | "PURCHASE_HOURLY"
  | "TRIAL"
  | "RENEWAL"
  | "REFUND"
  | "BILLING"
  | "BILLED_HOURLY";

export type TransactionStatus =
  | "PENDING"
  | "SUCCESS"
  | "FAILED"
  | "EXPIRED";

// Response dari backend sesuai dengan TransactionResponse di backend
export interface Transaction {
  id: number;
  invoice_id: string;
  description: string;
  amount: number;
  type: TransactionType;
  status: TransactionStatus;
  created_at: string;
  user_name?: string;
  user_avatar?: string;
}

// Response dari backend sesuai dengan TransactionListResponse di backend
export interface TransactionApiResponse {
  data: Transaction[];
  pagination: {
    total: number;
    per_page: number;
    current_page: number;
    last_page: number;
  };
  message?: string;
}

export interface TransactionFilters {
  type?: TransactionType | "";
  status?: TransactionStatus | "";
  search?: string;
  page?: number;
  limit?: number;
}

// Helper untuk mendapatkan label yang user-friendly
export const getTransactionTypeLabel = (type: TransactionType): string => {
  const labels: Record<TransactionType, string> = {
    TOPUP: "Top Up",
    PURCHASE_MONTHLY: "Pembelian Bulanan",
    PURCHASE_HOURLY: "Pembelian Per Jam",
    TRIAL: "Trial",
    RENEWAL: "Perpanjangan",
    REFUND: "Refund",
    BILLING: "Billing",
    BILLED_HOURLY: "Billing Per Jam"
  };
  return labels[type] || type;
};

export const getTransactionStatusLabel = (status: TransactionStatus): string => {
  const labels: Record<TransactionStatus, string> = {
    PENDING: "Menunggu",
    SUCCESS: "Berhasil",
    FAILED: "Gagal",
    EXPIRED: "Kedaluwarsa"
  };
  return labels[status] || status;
};

// Helper untuk mendapatkan warna badge berdasarkan status
export const getStatusBadgeClass = (status: TransactionStatus): string => {
  const classes: Record<TransactionStatus, string> = {
    PENDING: "badge-pill-outline-warning",
    SUCCESS: "badge-pill-outline-success",
    FAILED: "badge-pill-outline-danger",
    EXPIRED: "badge-pill-outline-gray"
  };
  return classes[status] || "badge-pill-outline-gray";
};

// Helper untuk mendapatkan warna badge berdasarkan tipe
export const getTypeBadgeClass = (type: TransactionType): string => {
  const classes: Record<TransactionType, string> = {
    TOPUP: "badge-pill badge-pill-primary",
    PURCHASE_MONTHLY: "badge-pill badge-pill-success",
    PURCHASE_HOURLY: "badge-pill badge-pill-info",
    TRIAL: "badge-pill badge-pill-secondary",
    RENEWAL: "badge-pill badge-pill-warning",
    REFUND: "badge-pill badge-pill-danger",
    BILLING: "badge-pill badge-pill-dark",
    BILLED_HOURLY: "badge-pill badge-pill-gray"
  };
  return classes[type] || "badge-pill badge-pill-gray";
};
