import 'next-auth';
import 'next-auth/jwt';

// The shape of the user object we store in the token/session
interface UserProfile {
  id: string;
  name?: string | null;
  email?: string | null;
  image?: string | null;
  username?: string | null;
  saldo?: number | null;
  roles?: string[];
}

declare module 'next-auth' {
  /**
   * Returned by `useSession`, `getSession` and received as a prop on the `SessionProvider` React Context
   */
  interface Session {
    accessToken?: string;
    user: UserProfile;
  }

  /**
   * The shape of the user object returned in the `authorize` callback
   */
  interface User extends UserProfile {
    accessToken: string;
  }
}

declare module 'next-auth/jwt' {
  /** Returned by the `jwt` callback and `getToken`, when using JWT sessions */
  interface JWT {
    accessToken: string;
    user: UserProfile;
  }
}
