import { useState, useEffect, useCallback } from 'react';
import { apiClientEnhanced } from '@/lib/apiClientEnhanced';

export interface NotificationData {
  account_id?: number;
  account_type?: string;
  username?: string;
  server_code?: string;
  amount?: number;
  invoice_id?: string;
  duration?: string;
  payment_method?: string;
}

export interface Notification {
  id: number;
  type: 'trial' | 'monthly' | 'hourly' | 'renewal' | 'topup' | 'payment' | 'billing';
  title: string;
  message: string;
  data?: NotificationData;
  is_read: boolean;
  created_at: string;
  read_at?: string;
}

export interface NotificationStats {
  unread_count: number;
  total_count: number;
}

export interface NotificationListResponse {
  notifications: Notification[];
  total: number;
  page: number;
  limit: number;
  unread_count: number;
}

export const useNotifications = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [stats, setStats] = useState<NotificationStats>({ unread_count: 0, total_count: 0 });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch notifications with pagination
  const fetchNotifications = useCallback(async (page = 1, limit = 10) => {
    try {
      setLoading(true);
      setError(null);
      
      const response = await apiClientEnhanced.get<NotificationListResponse>(
        `/users/me/notifications?page=${page}&limit=${limit}`
      );
      
      if (page === 1) {
        setNotifications(response.data.notifications);
      } else {
        setNotifications(prev => [...prev, ...response.data.notifications]);
      }
      
      setStats({
        unread_count: response.data.unread_count,
        total_count: response.data.total
      });
      
      return response.data;
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Gagal mengambil notifikasi';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch notification stats only
  const fetchStats = useCallback(async () => {
    try {
      const response = await apiClientEnhanced.get<NotificationStats>('/users/me/notifications/stats');
      setStats(response.data);
      return response.data;
    } catch (err: any) {
      console.error('Failed to fetch notification stats:', err);
      return null;
    }
  }, []);

  // Mark specific notifications as read
  const markAsRead = useCallback(async (notificationIds: number[]) => {
    try {
      await apiClientEnhanced.post('/users/me/notifications/mark-read', {
        notification_ids: notificationIds
      });
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => 
          notificationIds.includes(notif.id) 
            ? { ...notif, is_read: true, read_at: new Date().toISOString() }
            : notif
        )
      );
      
      // Update stats
      setStats(prev => ({
        ...prev,
        unread_count: Math.max(0, prev.unread_count - notificationIds.length)
      }));
      
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Gagal menandai notifikasi sebagai dibaca';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // Mark all notifications as read
  const markAllAsRead = useCallback(async () => {
    try {
      await apiClientEnhanced.post('/users/me/notifications/mark-all-read');
      
      // Update local state
      setNotifications(prev => 
        prev.map(notif => ({ 
          ...notif, 
          is_read: true, 
          read_at: new Date().toISOString() 
        }))
      );
      
      // Update stats
      setStats(prev => ({ ...prev, unread_count: 0 }));
      
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Gagal menandai semua notifikasi sebagai dibaca';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, []);

  // Delete notification
  const deleteNotification = useCallback(async (notificationId: number) => {
    try {
      await apiClientEnhanced.delete(`/users/me/notifications/${notificationId}`);
      
      // Update local state
      const deletedNotification = notifications.find(n => n.id === notificationId);
      setNotifications(prev => prev.filter(notif => notif.id !== notificationId));
      
      // Update stats
      setStats(prev => ({
        total_count: Math.max(0, prev.total_count - 1),
        unread_count: deletedNotification && !deletedNotification.is_read 
          ? Math.max(0, prev.unread_count - 1)
          : prev.unread_count
      }));
      
    } catch (err: any) {
      const errorMessage = err.response?.data?.error || 'Gagal menghapus notifikasi';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  }, [notifications]);

  // Auto-refresh stats periodically (polling approach)
  useEffect(() => {
    // Initial fetch
    fetchStats();
    
    // Set up polling every 30 seconds
    const interval = setInterval(fetchStats, 30000);
    
    return () => clearInterval(interval);
  }, [fetchStats]);

  return {
    notifications,
    stats,
    loading,
    error,
    fetchNotifications,
    fetchStats,
    markAsRead,
    markAllAsRead,
    deleteNotification,
    // Helper functions
    hasUnread: stats.unread_count > 0,
    unreadCount: stats.unread_count,
    totalCount: stats.total_count
  };
};
