import { createApiClientFromRequest } from '@/lib/apiClientEnhanced';
import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Mengambil parameter dari URL
    const page = request.nextUrl.searchParams.get('page') || '1';
    const limit = request.nextUrl.searchParams.get('limit') || '10';
    const type = request.nextUrl.searchParams.get('type');
    const search = request.nextUrl.searchParams.get('search');

    // Validasi tipe transaksi
    if (!type || !['TOPUP', 'PURCHASE', 'TRIAL'].includes(type.toUpperCase())) {
      return NextResponse.json({ error: 'Invalid transaction type' }, { status: 400 });
    }

    // Membuat query string
    const queryParams = new URLSearchParams();
    queryParams.append('page', page);
    queryParams.append('limit', limit);
    queryParams.append('type', type.toUpperCase());
    if (search) {
      queryParams.append('search', search);
    }

    // Membuat API client dengan token otentikasi
    const client = await createApiClientFromRequest(request);
    
    // Mengambil data transaksi pengguna yang sedang login
    const data = await client.get(`/users/me/transactions?${queryParams.toString()}`);

    return NextResponse.json(data);
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';
    console.error('Error fetching user transactions:', errorMessage);
    
    if (errorMessage.includes('Tidak terautentikasi')) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Log error lengkap untuk debugging
    console.error('Full error object:', error);

    return NextResponse.json(
      { error: 'Failed to fetch user transactions', details: errorMessage },
      { status: 500 }
    );
  }
}