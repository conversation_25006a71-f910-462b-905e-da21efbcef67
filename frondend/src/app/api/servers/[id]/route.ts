import { getToken } from "next-auth/jwt";
import { NextRequest, NextResponse } from "next/server";
import { createApiClientFromRequest } from "@/lib/apiClientEnhanced";

async function getAccessToken(req: NextRequest): Promise<string | null> {
  const token = await getToken({ req, secret: process.env.NEXTAUTH_SECRET });
  return token?.accessToken as string | null;
}

// GET - Fetch server by ID
export async function GET(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const accessToken = await getAccessToken(req);

    if (!accessToken) {
      return NextResponse.json(
        { message: "Tidak terautentikasi" },
        { status: 401 }
      );
    }

    const serverId = params.id;
    
    const apiClient = await createApiClientFromRequest(req);
    const data = await apiClient.get(`/servers/${serverId}`);
    return NextResponse.json(data);
  } catch (error) {
    console.error("Error fetching server:", error);
    
    // Jika error memiliki respons dari API backend, teruskan respons tersebut
    if (error && typeof error === 'object' && 'response' in error && error.response && typeof error.response === 'object' && 'data' in error.response && 'status' in error.response) {
      const status = typeof error.response.status === 'number' ? error.response.status : 500;
      return NextResponse.json(
        error.response.data,
        { status: status }
      );
    }
    
    return NextResponse.json(
      { message: "Terjadi kesalahan server" },
      { status: 500 }
    );
  }
}

// PUT - Update server by ID
export async function PUT(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const accessToken = await getAccessToken(req);

    if (!accessToken) {
      return NextResponse.json(
        { message: "Tidak terautentikasi" },
        { status: 401 }
      );
    }

    const serverId = params.id;
    const data = await req.json();
    
    const apiClient = await createApiClientFromRequest(req);
    const responseData = await apiClient.put(`/servers/${serverId}`, data);
    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error updating server:", error);
    
    // Jika error memiliki respons dari API backend, teruskan respons tersebut
    if (error && typeof error === 'object' && 'response' in error && error.response && typeof error.response === 'object' && 'data' in error.response && 'status' in error.response) {
      const status = typeof error.response.status === 'number' ? error.response.status : 500;
      return NextResponse.json(
        error.response.data,
        { status: status }
      );
    }
    
    return NextResponse.json(
      { message: "Terjadi kesalahan server" },
      { status: 500 }
    );
  }
}

// DELETE - Delete server by ID
export async function DELETE(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const accessToken = await getAccessToken(req);

    if (!accessToken) {
      return NextResponse.json(
        { message: "Tidak terautentikasi" },
        { status: 401 }
      );
    }

    const serverId = params.id;
    
    const apiClient = await createApiClientFromRequest(req);
    const responseData = await apiClient.delete(`/servers/${serverId}`);
    return NextResponse.json(responseData);
  } catch (error) {
    console.error("Error deleting server:", error);
    
    // Jika error memiliki respons dari API backend, teruskan respons tersebut
    if (error && typeof error === 'object' && 'response' in error && error.response && typeof error.response === 'object' && 'data' in error.response && 'status' in error.response) {
      const status = typeof error.response.status === 'number' ? error.response.status : 500;
      return NextResponse.json(
        error.response.data,
        { status: status }
      );
    }
    
    return NextResponse.json(
      { message: "Terjadi kesalahan server" },
      { status: 500 }
    );
  }
}