import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createApiClientFromRequest } from '@/lib/apiClientEnhanced';

export interface PaymentChannel {
  group: string;
  code: string;
  name: string;
  type: string;
  fee_merchant: {
    flat: number;
    percent: number;
  };
  fee_customer: {
    flat: number;
    percent: number;
  };
  total_fee: {
    flat: number;
    percent: string;
  };
  minimum_fee: number | null;
  maximum_fee: number | null;
  minimum_amount: number;
  maximum_amount: number;
  icon_url: string;
  active: boolean;
}

export interface PaymentChannelsResponse {
  success: boolean;
  message: string;
  data: PaymentChannel[];
}

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  try {
    const apiClient = await createApiClientFromRequest(req);
    const backendData: PaymentChannelsResponse = await apiClient.get('/payment/channels');
    
    console.log('Payment Channels Data:', JSON.stringify(backendData, null, 2));

    return NextResponse.json(backendData, { status: 200 });

  } catch (error: any) {
    console.error('Error in payment channels API route:', error);
    
    // Jika error dari backend API, teruskan response asli
    if (error.response && error.response.data) {
      return NextResponse.json(error.response.data, { status: error.response.status || 500 });
    }
    
    // Fallback untuk error lainnya
    return NextResponse.json({ 
      error: 'Terjadi kesalahan saat mengambil data payment channels',
      message: 'Internal server error' 
    }, { status: 500 });
  }
}
