import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { createApiClientFromRequest } from '@/lib/apiClientEnhanced';

interface PurchasePayload {
  username: string;
  kode_server: string;
  durasi: number; // dalam bulan untuk 'monthly', dalam hari untuk 'trial'
  subscription_type: 'monthly' | 'trial' | 'hourly';
  account_type: 'trojan' | 'vmess' | 'vless';
}

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { kode_server, durasi, subscription_type, account_type, username, payment_method, tripay_method } = body;

    // Validasi dasar untuk field yang harus selalu ada
    if (!kode_server || !durasi || !subscription_type || !account_type) {
      return NextResponse.json({ message: 'Data tidak lengkap: kode_server, durasi, tipe langganan, dan tipe akun diperlukan.' }, { status: 400 });
    }

    // Validasi untuk username: hanya diperlukan untuk monthly dan hourly, tidak untuk trial
    if (subscription_type !== 'trial' && (username === undefined || username === null)) {
        return NextResponse.json({ message: 'Data tidak lengkap: username diperlukan untuk langganan bulanan dan per jam.' }, { status: 400 });
    }

    // Tentukan endpoint backend berdasarkan subscription_type
    let endpoint = '';
    let backendPayload: any = {};

    if (subscription_type === 'trial') {
      endpoint = '/purchase/trial';
      backendPayload = {
        kode_server,
        protocol: account_type // trojan, vmess, vless
      };
    } else if (subscription_type === 'monthly') {
      endpoint = '/purchase/monthly';

      // Tentukan pembayaran dan metode berdasarkan pilihan user
      const pembayaran = payment_method || 'SALDO';
      const metode = pembayaran === 'TRIPAY' ? (tripay_method || 'QRIS') : 'user_saldo';

      backendPayload = {
        kode_server,
        bulan: durasi,
        username,
        protocol: account_type,
        pembayaran,
        metode
      };
    } else if (subscription_type === 'hourly') {
      endpoint = '/purchase/hourly';
      backendPayload = {
        kode_server,
        username,
        protocol: account_type
      };
    } else {
      return NextResponse.json({ message: 'Tipe langganan tidak valid' }, { status: 400 });
    }

    console.log('Frontend received body:', JSON.stringify(body, null, 2));
    console.log('Sending to backend endpoint:', endpoint);
    console.log('Backend payload:', JSON.stringify(backendPayload, null, 2));

    const apiClient = await createApiClientFromRequest(req);
    const backendData = await apiClient.post(endpoint, backendPayload);
    console.log('Backend Response Data:', JSON.stringify(backendData, null, 2));

    // Tambahkan informasi untuk redirect ke response
    const responseData = {
      ...backendData,
      redirect_info: {
        subscription_type,
        username: subscription_type === 'trial' ? backendData.username || backendData.data?.username : username,
        kode_server,
        should_redirect: true
      }
    };

    return NextResponse.json(responseData, { status: 200 });

  } catch (error: any) {
    console.error('Error in purchase API route:', error);

    // Jika error dari backend API, teruskan response asli
    if (error.response && error.response.data) {
      return NextResponse.json(error.response.data, { status: error.response.status || 500 });
    }

    // Jika error dari apiClient yang sudah diformat
    if (error.message && error.message !== 'Network Error') {
      return NextResponse.json({
        error: error.message,
        details: error.details || null
      }, { status: 500 });
    }

    // Fallback untuk error lainnya
    return NextResponse.json({
      error: 'Terjadi kesalahan internal pada server',
      message: 'Internal server error'
    }, { status: 500 });
  }
}
