import { getToken } from "next-auth/jwt";
import { createApiClientFromRequest } from "@/lib/apiClientEnhanced";
import { NextResponse, NextRequest } from "next/server";

export async function PUT(request: NextRequest) {
  const session = await getToken({ req: request, secret: process.env.NEXTAUTH_SECRET });

  if (!session || !session.accessToken) {
    return NextResponse.json({ message: 'Unauthorized' }, { status: 401 });
  }

  try {
    const body = await request.json();
    const apiClient = await createApiClientFromRequest(request);
    const data = await apiClient.put('/profile/change-password', body);
    return NextResponse.json(data, { status: 200 });

  } catch (error) {
    console.error("Error in change-password API route:", error);
    return NextResponse.json({ message: 'An internal server error occurred' }, { status: 500 });
  }
}
