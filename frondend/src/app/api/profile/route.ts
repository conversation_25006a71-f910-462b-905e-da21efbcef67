import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { NextRequest, NextResponse } from "next/server";
import { createApiClientFromRequest } from "@/lib/apiClientEnhanced";

export async function PUT(req: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session.accessToken) {
    return NextResponse.json({ message: "Not authenticated" }, { status: 401 });
  }

  const body = await req.json();

  try {
    const apiClient = await createApiClientFromRequest(req);
    const data = await apiClient.put('/users/me', body);

    return NextResponse.json(data, { status: 200 });

  } catch (error) {
    console.error('Profile update error:', error);
    return NextResponse.json({ message: 'An unexpected error occurred' }, { status: 500 });
  }
}
