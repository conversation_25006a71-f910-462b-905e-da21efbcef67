import { Suspense } from 'react';
import { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Manajemen Pengguna - Admin',
  description: 'Halaman untuk mengelola data pengguna.',
};
import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';
import UserTable from '@/components/Tables/UserTable';
import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';

async function getUsers() {
  const apiClient = await createAuthenticatedApiClient();
  
  if (!apiClient) {
    throw new Error("Tidak terautentikasi atau token tidak ada.");
  }

  try {
    const data = await apiClient.get('/admin/users');
    return data.users;
  } catch (error) {
    console.error('Gagal mengambil data pengguna:', error);
    throw new Error("Gagal mengambil data pengguna.");
  }
}

const UserManagementPage = async () => {
  const users = await getUsers();

  return (
    <>
      <Breadcrumb pageName="Manajemen Pengguna" />
      <div className="flex flex-col gap-10">
        <Suspense fallback={<div>Memuat pengguna...</div>}>
          <UserTable users={users} />
        </Suspense>
      </div>
    </>
  );
};

export default UserManagementPage;
