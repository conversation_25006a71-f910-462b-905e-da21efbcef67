import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';

export async function getOverviewData() {
  try {
    const apiClient = await createAuthenticatedApiClient();
    const data = await apiClient.get('/admin/dashboard/overview');
    return data;
  } catch (error) {
    console.error('Error fetching overview data:', error);
    // Return default data in case of error
    return {
      views: { value: 0, change: 0 },
      profit: { value: 0, change: 0 },
      products: { value: 0, change: 0 },
      users: { value: 0, change: 0 }
    };
  }
}

export async function getChatsData() {
  try {
    const apiClient = await createAuthenticatedApiClient();
    const data = await apiClient.get('/admin/dashboard/chats');
    return data;
  } catch (error) {
    console.error('Error fetching chats data:', error);
    // Return default data in case of error
    return [];
  }
}