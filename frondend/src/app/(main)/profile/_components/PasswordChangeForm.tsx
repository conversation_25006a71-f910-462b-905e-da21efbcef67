"use client";

import { useState } from "react";
import { useNotification } from "@/contexts/NotificationContext";

interface PasswordChangeFormProps {
  accessToken: string;
}

export default function PasswordChangeForm({ accessToken }: PasswordChangeFormProps) {
  const { addNotification } = useNotification();
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    old_password: "",
    new_password: "",
    confirmNewPassword: "",
  });
  const [loading, setLoading] = useState(false);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({ ...formData, [e.target.name]: e.target.value });
  };

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (formData.new_password !== formData.confirmNewPassword) {
      addNotification("Kata sandi baru tidak cocok.", "error");
      return;
    }

    setLoading(true);

    try {
      const response = await fetch(`/api/profile/change-password`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          old_password: formData.old_password,
          new_password: formData.new_password,
        }),
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.message || 'Gagal mengganti kata sandi.');
      }

      addNotification('Kata sandi berhasil diganti!', 'success');
      setFormData({ old_password: "", new_password: "", confirmNewPassword: "" });
      setIsEditing(false);
    } catch (err: any) {
      addNotification(err.message, 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setFormData({ old_password: "", new_password: "", confirmNewPassword: "" });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h4 className="text-lg font-semibold text-dark dark:text-white">Ubah Kata Sandi</h4>
          <p className="text-sm text-dark-6">Perbarui kata sandi untuk menjaga keamanan akun Anda</p>
        </div>
        {!isEditing && (
          <button
            onClick={() => setIsEditing(true)}
            className="inline-flex items-center gap-2 rounded-lg border border-primary bg-primary/10 px-4 py-2 text-sm font-medium text-primary transition-all duration-300 hover:bg-primary hover:text-white"
          >
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit
          </button>
        )}
      </div>

      {!isEditing ? (
        <div className="rounded-lg border border-stroke bg-gray-1 p-6 text-center dark:border-dark-3 dark:bg-dark-2">
          <div className="mx-auto mb-4 h-16 w-16 rounded-full bg-primary/10 flex items-center justify-center">
            <svg className="h-8 w-8 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
          </div>
          <h5 className="text-lg font-medium text-dark dark:text-white mb-2">Keamanan Kata Sandi</h5>
          <p className="text-sm text-dark-6 mb-4">Kata sandi Anda saat ini aman. Klik tombol "Edit" di atas untuk mengubah kata sandi.</p>
          <div className="flex items-center justify-center gap-2 text-xs text-green">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Kata sandi terenkripsi dengan aman
          </div>
        </div>
      ) : (
        <form onSubmit={handleSubmit} className="space-y-6">
        {/* Current Password */}
        <div className="space-y-2">
          <label className="form-label flex items-center gap-2">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
            </svg>
            Kata Sandi Saat Ini
          </label>
          <div className="relative">
            <input
              type="password"
              name="old_password"
              placeholder="Masukkan kata sandi Anda saat ini"
              value={formData.old_password}
              onChange={handleChange}
              className="form-input pr-12"
              required
            />
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <svg className="h-5 w-5 text-dark-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
            </div>
          </div>
        </div>

        {/* New Password */}
        <div className="space-y-2">
          <label className="form-label flex items-center gap-2">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v-2l-4.586-4.586a1 1 0 010-1.414L8 5.414a1 1 0 011.414 0L11 7h2a2 2 0 012 2z" />
            </svg>
            Kata Sandi Baru
          </label>
          <div className="relative">
            <input
              type="password"
              name="new_password"
              placeholder="Masukkan kata sandi baru Anda"
              value={formData.new_password}
              onChange={handleChange}
              className="form-input pr-12"
              required
            />
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              <svg className="h-5 w-5 text-dark-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v-2l-4.586-4.586a1 1 0 010-1.414L8 5.414a1 1 0 011.414 0L11 7h2a2 2 0 012 2z" />
              </svg>
            </div>
          </div>
          <div className="space-y-1">
            <p className="text-xs text-dark-6">Kata sandi harus memenuhi kriteria berikut:</p>
            <ul className="space-y-1 text-xs text-dark-6">
              <li className="flex items-center gap-2">
                <div className={`h-1.5 w-1.5 rounded-full ${
                  formData.new_password.length >= 8 ? 'bg-green' : 'bg-gray-4'
                }`}></div>
                Minimal 8 karakter
              </li>
              <li className="flex items-center gap-2">
                <div className={`h-1.5 w-1.5 rounded-full ${
                  /[A-Z]/.test(formData.new_password) ? 'bg-green' : 'bg-gray-4'
                }`}></div>
                Mengandung huruf besar
              </li>
              <li className="flex items-center gap-2">
                <div className={`h-1.5 w-1.5 rounded-full ${
                  /[a-z]/.test(formData.new_password) ? 'bg-green' : 'bg-gray-4'
                }`}></div>
                Mengandung huruf kecil
              </li>
              <li className="flex items-center gap-2">
                <div className={`h-1.5 w-1.5 rounded-full ${
                  /[0-9]/.test(formData.new_password) ? 'bg-green' : 'bg-gray-4'
                }`}></div>
                Mengandung angka
              </li>
            </ul>
          </div>
        </div>

        {/* Confirm New Password */}
        <div className="space-y-2">
          <label className="form-label flex items-center gap-2">
            <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Konfirmasi Kata Sandi Baru
          </label>
          <div className="relative">
            <input
              type="password"
              name="confirmNewPassword"
              placeholder="Konfirmasi kata sandi baru Anda"
              value={formData.confirmNewPassword}
              onChange={handleChange}
              className={`form-input pr-12 ${
                formData.confirmNewPassword && formData.new_password !== formData.confirmNewPassword
                  ? 'border-red bg-red/5 focus:border-red focus:ring-red/20'
                  : ''
              }`}
              required
            />
            <div className="absolute right-3 top-1/2 -translate-y-1/2">
              {formData.confirmNewPassword && (
                formData.new_password === formData.confirmNewPassword ? (
                  <svg className="h-5 w-5 text-green" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                ) : (
                  <svg className="h-5 w-5 text-red" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                )
              )}
            </div>
          </div>
          {formData.confirmNewPassword && formData.new_password !== formData.confirmNewPassword && (
            <p className="text-xs text-red">Kata sandi tidak cocok</p>
          )}
        </div>

        {/* Security Notice */}
        <div className="rounded-lg border border-blue/20 bg-blue/10 p-4">
          <div className="flex gap-3">
            <svg className="h-5 w-5 text-blue mt-0.5 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <h5 className="text-sm font-medium text-blue mb-1">Keamanan Akun</h5>
              <p className="text-xs text-blue/80">
                Setelah mengubah kata sandi, Anda akan diminta untuk masuk kembali di semua perangkat untuk keamanan.
              </p>
            </div>
          </div>
        </div>

          {/* Submit Button */}
          <div className="flex justify-end gap-3 pt-4 border-t border-stroke dark:border-dark-3">
            <button
              type="button"
              onClick={handleCancel}
              disabled={loading}
              className="form-button-secondary disabled:cursor-not-allowed disabled:opacity-50"
            >
              <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              Batal
            </button>
            <button
              type="submit"
              disabled={loading || formData.new_password !== formData.confirmNewPassword || !formData.old_password || !formData.new_password}
              className="form-button-primary disabled:cursor-not-allowed disabled:opacity-50"
            >
              {loading ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                  Mengubah...
                </>
              ) : (
                <>
                  <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  Ubah Kata Sandi
                </>
              )}
            </button>
          </div>
        </form>
      )}
    </div>
  );
}
