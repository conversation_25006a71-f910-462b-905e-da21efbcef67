"use client";

import { useState } from "react";
import ProfileSettingsForm from "./ProfileSettingsForm";
import PasswordChangeForm from "./PasswordChangeForm";
import ProfilePhotoUpload from "./ProfilePhotoUpload";
import ProfileStats from "./ProfileStats";
import type { UserProfile } from "@/types/user";

interface UserStats {
  pay_bulanan: number;
  total_pay: number;
  batas_trial: number;
  trial: number;
  jumlah_akun_trial: number;
  jumlah_akun_hourly: number;
  jumlah_akun_month: number;
  jumlah_akun_trojan: number;
  jumlah_akun_vmess: number;
  jumlah_akun_vless: number;
  jumlah_akun_ssh: number;
  total_account: number;
}

interface SettingsContainerProps {
  user: UserProfile;
  accessToken: string;
  userStats: UserStats;
  onPhotoUpdate?: (newPhotoUrl: string) => void;
}





export default function SettingsContainer({ user, accessToken, userStats, onPhotoUpdate }: SettingsContainerProps) {
  const [activeTab, setActiveTab] = useState<'stats' | 'settings'>('stats');

  return (
    <div className="rounded-[10px] bg-white p-6 shadow-1 dark:bg-gray-dark">
      {/* Tab Navigation */}
      <div className="flex space-x-1 rounded-lg bg-gray-1 p-1 dark:bg-dark-2 mb-6">
        <button
          onClick={() => setActiveTab('stats')}
          className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 ${
            activeTab === 'stats'
              ? 'bg-white text-primary shadow-sm dark:bg-dark dark:text-primary'
              : 'text-dark-6 hover:text-dark dark:hover:text-white'
          }`}
        >
          Statistik
        </button>
        <button
          onClick={() => setActiveTab('settings')}
          className={`flex-1 rounded-md px-3 py-2 text-sm font-medium transition-all duration-200 ${
            activeTab === 'settings'
              ? 'bg-white text-primary shadow-sm dark:bg-dark dark:text-primary'
              : 'text-dark-6 hover:text-dark dark:hover:text-white'
          }`}
        >
          Pengaturan Akun
        </button>
      </div>



      {/* Tab Content */}
      <div className="mt-6">
        {activeTab === 'stats' && (
          <div className="space-y-6">
            <ProfileStats user={user} userStats={userStats} />
          </div>
        )}
        
        {activeTab === 'settings' && (
          <div className="space-y-8">
            {/* Profile Photo Upload */}
            <ProfilePhotoUpload user={user} accessToken={accessToken} onPhotoUpdate={onPhotoUpdate} />
            
            {/* Profile Settings Form */}
            <ProfileSettingsForm user={user} accessToken={accessToken} />
            
            {/* Password Change Form */}
            <PasswordChangeForm accessToken={accessToken} />
          </div>
        )}
      </div>
    </div>
  );
}
