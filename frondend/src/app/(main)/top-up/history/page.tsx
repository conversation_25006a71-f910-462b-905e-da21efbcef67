import { Metadata } from 'next';
import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';
import TopUpHistoryTable, { TopUpHistoryApiResponse } from "@/components/Tables/TopUpHistoryTable";
import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';

export const metadata: Metadata = {
  title: `Riwayat Top Up | ${process.env.NEXT_PUBLIC_APP_NAME}`,
  description: `Halaman riwayat top up untuk melihat semua transaksi pengisian saldo <PERSON>a.`,
};

interface TopUpHistoryPageProps {
  searchParams?: Promise<{
    page?: string | string[];
    limit?: string | string[];
    search?: string | string[];
  }>;
}

const fetchTopUpHistoryData = async (searchParams: {
    page?: string | string[];
    limit?: string | string[];
    search?: string | string[];
  } | undefined): Promise<TopUpHistoryApiResponse> => {
    const apiClient = await createAuthenticatedApiClient();
    
    if (!apiClient) {
        return { 
            data: [], 
            pagination: {
                total: 0,
                per_page: 10,
                current_page: 1,
                last_page: 1
            }, 
            message: "Authentication required" 
        };
    }

    try {
        // Mengambil parameter dari URL
        const page = searchParams?.page ? Number(Array.isArray(searchParams.page) ? searchParams.page[0] : searchParams.page) : 1;
        const limit = searchParams?.limit ? Number(Array.isArray(searchParams.limit) ? searchParams.limit[0] : searchParams.limit) : 10;
        const search = searchParams?.search ? (Array.isArray(searchParams.search) ? searchParams.search[0] : searchParams.search) : '';

        // Membuat query string
        const queryParams = new URLSearchParams();
        queryParams.append('page', String(page));
        queryParams.append('limit', String(limit));
        queryParams.append('type', 'TOPUP');
        if (search) {
            queryParams.append('search', search);
        }

        // Mengambil data dari API
        const data = await apiClient.get(`/users/me/transactions?${queryParams.toString()}`);
        
        // Data sudah dalam format yang diharapkan oleh komponen
        return data || {
            data: [],
            pagination: {
                total: 0,
                per_page: limit,
                current_page: page,
                last_page: 1
            },
            message: "Tidak ada riwayat top up ditemukan."
        };
    } catch (error) {
        console.error('Error fetching top up history:', error);
        return { 
            data: [], 
            pagination: {
                total: 0,
                per_page: 10,
                current_page: 1,
                last_page: 1
            },
            message: "Gagal mengambil data riwayat top up. Silakan coba lagi nanti." 
        };
    }
};

const TopUpHistoryPage = async ({ searchParams }: TopUpHistoryPageProps) => {
  const resolvedSearchParams = await searchParams;
  const initialTopUpHistoryData = await fetchTopUpHistoryData(resolvedSearchParams);

  return (
    <>
      <Breadcrumb pageName="Riwayat Top Up" />

      <div className="flex flex-col gap-10">
        <TopUpHistoryTable initialData={initialTopUpHistoryData} />
      </div>
    </>
  );
};

export default TopUpHistoryPage;