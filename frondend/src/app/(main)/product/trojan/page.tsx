import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';
import { Server } from '@/types/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { createAuthenticatedApiClient } from '@/lib/apiClientEnhanced';
import ServerListClient from './ServerListClient';
import { Metadata } from 'next';

export const metadata: Metadata = {
    title: 'Beli Layanan Trojan',
    description: '<PERSON><PERSON>h dan beli layanan Trojan VPN dari berbagai server.',
};

async function fetchServers(token: string | undefined): Promise<Server[]> {
    if (!token) {
        console.error('Authentication token not found.');
        return [];
    }
    try {
        const apiClient = await createAuthenticatedApiClient();
        const data = await apiClient.get('/servers');

        const transformedServers = data.servers;

        // Filter servers where trojan is enabled
        return transformedServers.filter((server: Server) => server.trojan === 'enabled' || server.trojan === 'enable');
    } catch (error) {
        console.error("An error occurred while fetching servers:", error);
        return [];
    }
}

export default async function TrojanPage() {
    const session = await getServerSession(authOptions);
    const servers = await fetchServers(session?.accessToken);

    return (
        <>
            <Breadcrumb pageName="Pembelian Layanan Trojan" />
            
            {/* Hero Section */}
            <section className="relative overflow-hidden bg-gradient-to-br from-pink-50 via-purple-50 to-indigo-50 dark:from-pink-900/20 dark:via-purple-900/20 dark:to-indigo-900/20">
                <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
                <div className="relative mx-auto max-w-7xl px-4 py-20 md:px-6 lg:py-28 xl:py-32 2xl:px-0">
                    <div className="mx-auto max-w-4xl text-center">
                        <div className="mb-8 inline-flex items-center rounded-full bg-pink-100 px-4 py-2 text-sm font-medium text-pink-800 dark:bg-pink-900/30 dark:text-pink-300">
                            <svg className="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                            </svg>
                            Trojan Protocol - Teknologi Terdepan
                        </div>
                        
                        <h1 className="mb-6 text-4xl font-bold tracking-tight text-gray-900 dark:text-white sm:text-5xl lg:text-6xl">
                            Layanan <span className="bg-gradient-to-r from-pink-600 to-purple-600 bg-clip-text text-transparent">Trojan</span> Premium
                        </h1>
                        
                        <p className="mx-auto mb-8 max-w-2xl text-lg leading-8 text-gray-600 dark:text-gray-300">
                            Nikmati koneksi internet yang cepat, aman, dan stabil dengan protokol Trojan terbaru. 
                            Teknologi enkripsi canggih untuk privasi maksimal dan performa optimal.
                        </p>
                        
                        <div className="flex flex-col items-center justify-center gap-4 sm:flex-row sm:gap-6">
                            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100 dark:bg-green-900/30">
                                    <svg className="h-4 w-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                                    </svg>
                                </div>
                                <span>Enkripsi AES-256</span>
                            </div>
                            
                            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
                                    <svg className="h-4 w-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                                    </svg>
                                </div>
                                <span>Kecepatan Tinggi</span>
                            </div>
                            
                            <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                                <div className="flex h-8 w-8 items-center justify-center rounded-full bg-purple-100 dark:bg-purple-900/30">
                                    <svg className="h-4 w-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                </div>
                                <span>99.9% Uptime</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                {/* Decorative Elements */}
                <div className="absolute left-1/2 top-0 -z-10 -translate-x-1/2 blur-3xl xl:-top-6" aria-hidden="true">
                    <div className="aspect-[1155/678] w-[72.1875rem] bg-gradient-to-tr from-pink-300 to-purple-300 opacity-30" style={{
                        clipPath: 'polygon(74.1% 44.1%, 100% 61.6%, 97.5% 26.9%, 85.5% 0.1%, 80.7% 2%, 72.5% 32.5%, 60.2% 62.4%, 52.4% 68.1%, 47.5% 58.3%, 45.2% 34.5%, 27.5% 76.7%, 0.1% 64.9%, 17.9% 100%, 27.6% 76.8%, 76.1% 97.7%, 74.1% 44.1%)'
                    }}></div>
                </div>
            </section>
            
            {/* Main Content */}
            <div className="mx-auto max-w-7xl px-4 md:px-6 2xl:px-0">
                <div className="py-16 lg:py-20">
                    <ServerListClient servers={servers} />
                </div>
            </div>
        </>
    );
}

