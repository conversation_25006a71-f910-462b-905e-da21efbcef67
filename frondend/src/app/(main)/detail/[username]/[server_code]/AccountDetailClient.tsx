'use client';

import { useEffect, useState } from 'react';
import Breadcrumb from '@/components/Breadcrumbs/Breadcrumb';
import { Button } from '@/components/ui-elements/button';
import { useNotification } from '@/contexts/NotificationContext';
import { useRouter } from 'next/navigation';
import { CopyButton } from '@/components/ui-elements/copy-button';
import { formatDate } from '@/lib/utils';

interface ConnectionLink {
  name: string;
  url: string;
}

interface ServerInfo {
  server_id: number;
  nama: string;
  kode: string;
  domain: string;
  negara: string;
  nama_isp: string;
  harga_member: number;
  harga_reseller: number;
  ssh: string;
  trojan: string;
  vmess: string;
  vless: string;
  created_at: string;
  updated_at: string;
}

interface AccountDetail {
  username: string;
  uuid: string;
  account_type: string;
  status: string;
  protocol: string;
  expired_date: string;
  data_limit_gb: number;
  used_traffic_gb: number;
  subscription_url: string;
  connection_links: ConnectionLink[];
  server: ServerInfo;
}

interface AccountDetailClientProps {
  accountDetail: AccountDetail;
  error?: string;
}

const AccountDetailClient = ({ accountDetail, error }: AccountDetailClientProps) => {
  const router = useRouter();
  const { addNotification } = useNotification();
  const [isRenewing, setIsRenewing] = useState(false);
  const [isSubscriptionOpen, setIsSubscriptionOpen] = useState(false);
  const [isConnectionsOpen, setIsConnectionsOpen] = useState(false);

  useEffect(() => {
    if (accountDetail) {
      document.title = `Detail Akun: ${accountDetail.username}`;
    } else if (error) {
      document.title = 'Error Memuat Detail Akun';
    }
  }, [accountDetail, error]);

  const handleRenew = async () => {
    setIsRenewing(true);
    try {
      const res = await fetch('/api/renew', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: accountDetail.username,
          server_code: accountDetail.server.kode,
          account_type: accountDetail.account_type,
        }),
      });

      const data = await res.json();

      if (res.ok) {
        addNotification('Akun berhasil diperpanjang!', 'success');
        // Refresh halaman untuk mendapatkan data terbaru
        router.refresh();
      } else {
        addNotification(data.error || 'Gagal memperpanjang akun', 'error');
      }
    } catch (err) {
      addNotification('Terjadi kesalahan saat memperpanjang akun', 'error');
    } finally {
      setIsRenewing(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status.toLowerCase()) {
      case 'active':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';
      case 'expired':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';
      case 'inactive':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const getProtocolBadgeClass = (protocol: string) => {
    switch (protocol.toLowerCase()) {
      case 'trojan':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300';
      case 'vmess':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';
      case 'vless':
        return 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-300';
      case 'ssh':
        return 'bg-teal-100 text-teal-800 dark:bg-teal-900 dark:text-teal-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300';
    }
  };

  const usagePercentage = accountDetail.data_limit_gb > 0
    ? Math.min(Math.round((accountDetail.used_traffic_gb / accountDetail.data_limit_gb) * 100), 100)
    : 0;

  return (
    <>
      <Breadcrumb pageName="Detail Akun" />

      <div className="space-y-8">
        {/* Hero Header Section */}
        <div className="relative overflow-hidden bg-gradient-to-br from-indigo-600 via-purple-600 to-pink-600 rounded-2xl shadow-2xl">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="absolute inset-0 bg-gradient-to-r from-blue-600/30 to-transparent"></div>
          
          {/* Decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-white/10 rounded-full -translate-y-32 translate-x-32"></div>
          <div className="absolute bottom-0 left-0 w-48 h-48 bg-white/5 rounded-full translate-y-24 -translate-x-24"></div>
          
          <div className="relative p-8 lg:p-12">
            <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-8">
              <div className="space-y-6">
                <div className="flex items-center gap-6">
                  <div className="relative">
                    <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-2xl flex items-center justify-center border border-white/30 shadow-xl">
                      <span className="text-3xl font-bold text-white">
                        {accountDetail.protocol.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <div className="absolute -top-2 -right-2 w-6 h-6 bg-green-400 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
                  </div>
                  
                  <div className="space-y-3">
                    <div className="flex items-center gap-2 sm:gap-4 flex-wrap">
                      <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-white drop-shadow-lg">
                        {accountDetail.username}
                      </h1>
                      <div className="bg-white/20 backdrop-blur-sm rounded-lg p-1 sm:p-2 border border-white/30 flex-shrink-0">
                        <div className="scale-75 sm:scale-100">
                          <CopyButton text={accountDetail.username} />
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-1.5 sm:gap-2">
                      <div className="bg-black/30 backdrop-blur-sm rounded-lg px-2 py-1 border border-white/20 flex-shrink-0 min-w-0 flex-1">
                        <p className="text-xs sm:text-sm text-white/90 font-mono truncate">
                          UUID: {accountDetail.uuid.substring(0, 8)}...{accountDetail.uuid.slice(-8)}
                        </p>
                      </div>
                      <div className="bg-white/20 backdrop-blur-sm rounded-lg p-0.5 sm:p-1 border border-white/30 flex-shrink-0">
                        <div className="scale-[0.6] sm:scale-75">
                          <CopyButton text={accountDetail.uuid} />
                        </div>
                      </div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center gap-2 text-white/90">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" clipRule="evenodd" />
                        </svg>
                        <span className="font-medium">{accountDetail.server.nama}</span>
                        <span className="text-white/70">•</span>
                        <span>{accountDetail.server.negara}</span>
                      </div>
                      <div className="flex items-center gap-2 text-white/80 text-sm">
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4.083 9h1.946c.089-1.546.383-2.97.837-4.118A6.004 6.004 0 004.083 9zM10 2a8 8 0 100 16 8 8 0 000-16zm0 2c-.076 0-.232.032-.465.262-.238.234-.497.623-.737 1.182-.389.907-.673 2.142-.766 3.556h3.936c-.093-1.414-.377-2.649-.766-3.556-.24-.56-.5-.948-.737-1.182C10.232 4.032 10.076 4 10 4zm3.971 5c-.089-1.546-.383-2.97-.837-4.118A6.004 6.004 0 0115.917 9h-1.946zm-2.003 2H8.032c.093 1.414.377 2.649.766 3.556.24.56.5.948.737 ************.389.262.465.262.076 0 .232-.032.465-.262.238-.234.498-.623.737-1.182.389-.907.673-2.142.766-3.556zm1.166 4.118c.454-1.147.748-2.572.837-4.118h1.946a6.004 6.004 0 01-2.783 4.118zm-6.268 0C6.412 13.97 6.118 12.546 6.03 11H4.083a6.004 6.004 0 002.783 4.118z" clipRule="evenodd" />
                        </svg>
                        <span>{accountDetail.server.domain}</span>
                        <span className="text-white/60">•</span>
                        <span>{accountDetail.server.nama_isp}</span>
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-3">
                  <div className={`px-4 py-2 rounded-full text-sm font-semibold backdrop-blur-sm border ${
                    accountDetail.status.toLowerCase() === 'active' 
                      ? 'bg-green-500/30 text-green-100 border-green-400/50' 
                      : accountDetail.status.toLowerCase() === 'expired'
                      ? 'bg-red-500/30 text-red-100 border-red-400/50'
                      : 'bg-yellow-500/30 text-yellow-100 border-yellow-400/50'
                  }`}>
                    <div className="flex items-center gap-2">
                      <div className={`w-2 h-2 rounded-full ${
                        accountDetail.status.toLowerCase() === 'active' ? 'bg-green-400' :
                        accountDetail.status.toLowerCase() === 'expired' ? 'bg-red-400' : 'bg-yellow-400'
                      } animate-pulse`}></div>
                      {accountDetail.status.toUpperCase()}
                    </div>
                  </div>
                  
                  <div className={`px-4 py-2 rounded-full text-sm font-semibold backdrop-blur-sm border ${
                    accountDetail.protocol.toLowerCase() === 'trojan' 
                      ? 'bg-purple-500/30 text-purple-100 border-purple-400/50'
                      : accountDetail.protocol.toLowerCase() === 'vmess'
                      ? 'bg-blue-500/30 text-blue-100 border-blue-400/50'
                      : accountDetail.protocol.toLowerCase() === 'vless'
                      ? 'bg-indigo-500/30 text-indigo-100 border-indigo-400/50'
                      : 'bg-teal-500/30 text-teal-100 border-teal-400/50'
                  }`}>
                    {accountDetail.protocol.toUpperCase()}
                  </div>
                </div>
              </div>
              
              <div className="flex flex-col gap-4">
                <Button
                  label={isRenewing ? "Memproses..." : "🚀 Perpanjang Akun"}
                  variant="primary"
                  className="px-8 py-4 font-semibold text-lg bg-white/20 backdrop-blur-sm border border-white/30 text-white hover:bg-white/30 transition-all duration-300 shadow-xl"
                  onClick={handleRenew}
                  disabled={isRenewing}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Stats Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {/* Data Usage Card */}
          <div className="group relative overflow-hidden bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-lg border border-blue-200/50 dark:border-gray-700 p-6 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-blue-500/10 rounded-xl">
                  <svg className="w-6 h-6 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                  </svg>
                </div>
                <div className="text-right">
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">
                    {usagePercentage}%
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Penggunaan Data</p>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Terpakai</span>
                  <span className="font-medium text-gray-900 dark:text-white">{accountDetail.used_traffic_gb.toFixed(2)} GB</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600 dark:text-gray-400">Total Limit</span>
                  <span className="font-medium text-gray-900 dark:text-white">{accountDetail.data_limit_gb.toFixed(2)} GB</span>
                </div>
                
                <div className="relative">
                  <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                    <div 
                      className={`h-3 rounded-full transition-all duration-1000 ease-out ${
                        usagePercentage > 90 ? 'bg-gradient-to-r from-red-500 to-red-600' :
                        usagePercentage > 75 ? 'bg-gradient-to-r from-yellow-500 to-orange-500' :
                        'bg-gradient-to-r from-green-500 to-emerald-500'
                      }`}
                      style={{ width: `${Math.min(usagePercentage, 100)}%` }}
                    ></div>
                  </div>
                  <div className="absolute -top-1 transition-all duration-1000 ease-out" style={{ left: `${Math.min(usagePercentage, 100)}%` }}>
                    <div className={`w-5 h-5 rounded-full border-2 border-white shadow-lg ${
                      usagePercentage > 90 ? 'bg-red-500' :
                      usagePercentage > 75 ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}></div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Status Card */}
          <div className="group relative overflow-hidden bg-gradient-to-br from-emerald-50 to-green-100 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-lg border border-emerald-200/50 dark:border-gray-700 p-6 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-emerald-500/5 to-green-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative">
              <div className="flex items-center justify-between mb-4">
                <div className={`p-3 rounded-xl ${
                  accountDetail.status.toLowerCase() === 'active' ? 'bg-green-500/10' :
                  accountDetail.status.toLowerCase() === 'expired' ? 'bg-red-500/10' :
                  'bg-yellow-500/10'
                }`}>
                  <div className={`w-6 h-6 rounded-full ${
                    accountDetail.status.toLowerCase() === 'active' ? 'bg-green-500' :
                    accountDetail.status.toLowerCase() === 'expired' ? 'bg-red-500' :
                    'bg-yellow-500'
                  } animate-pulse`}></div>
                </div>
                <div className="text-right">
                  <p className="text-2xl font-bold text-gray-900 dark:text-white capitalize">
                    {accountDetail.status}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Status Akun</p>
                </div>
              </div>
              
              <div className={`p-4 rounded-xl border-2 border-dashed ${
                accountDetail.status.toLowerCase() === 'active' 
                  ? 'border-green-300 bg-green-50 dark:bg-green-900/20 dark:border-green-700' 
                  : accountDetail.status.toLowerCase() === 'expired'
                  ? 'border-red-300 bg-red-50 dark:bg-red-900/20 dark:border-red-700'
                  : 'border-yellow-300 bg-yellow-50 dark:bg-yellow-900/20 dark:border-yellow-700'
              }`}>
                <p className={`text-sm font-medium ${
                  accountDetail.status.toLowerCase() === 'active' ? 'text-green-700 dark:text-green-300' :
                  accountDetail.status.toLowerCase() === 'expired' ? 'text-red-700 dark:text-red-300' :
                  'text-yellow-700 dark:text-yellow-300'
                }`}>
                  {accountDetail.status.toLowerCase() === 'active' ? '✅ Akun aktif dan siap digunakan' :
                   accountDetail.status.toLowerCase() === 'expired' ? '⚠️ Akun telah kedaluwarsa' :
                   '⏳ Akun dalam status pending'}
                </p>
              </div>
            </div>
          </div>

          {/* Expiry Date Card */}
          <div className="group relative overflow-hidden bg-gradient-to-br from-purple-50 to-pink-100 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-lg border border-purple-200/50 dark:border-gray-700 p-6 hover:shadow-xl transition-all duration-300">
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            <div className="relative">
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 bg-purple-500/10 rounded-xl">
                  <svg className="w-6 h-6 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3a4 4 0 118 0v4m-4 8a2 2 0 100-4 2 2 0 000 4zm6-6V7a2 2 0 00-2-2H6a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2z" />
                  </svg>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold text-gray-900 dark:text-white">
                    {formatDate(new Date(accountDetail.expired_date))}
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">Tanggal Kedaluwarsa</p>
                </div>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm text-gray-600 dark:text-gray-400">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" />
                  </svg>
                  <span>
                    {(() => {
                      const now = new Date();
                      const expiry = new Date(accountDetail.expired_date);
                      const diffTime = expiry.getTime() - now.getTime();
                      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                      
                      if (diffDays < 0) return 'Sudah kedaluwarsa';
                      if (diffDays === 0) return 'Kedaluwarsa hari ini';
                      if (diffDays === 1) return 'Kedaluwarsa besok';
                      return `${diffDays} hari lagi`;
                    })()} 
                  </span>
                </div>
                
                <div className={`p-3 rounded-lg ${
                  (() => {
                    const now = new Date();
                    const expiry = new Date(accountDetail.expired_date);
                    const diffTime = expiry.getTime() - now.getTime();
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                    
                    if (diffDays < 0) return 'bg-red-100 dark:bg-red-900/20';
                    if (diffDays <= 7) return 'bg-yellow-100 dark:bg-yellow-900/20';
                    return 'bg-green-100 dark:bg-green-900/20';
                  })()
                }`}>
                  <p className={`text-xs font-medium ${
                    (() => {
                      const now = new Date();
                      const expiry = new Date(accountDetail.expired_date);
                      const diffTime = expiry.getTime() - now.getTime();
                      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                      
                      if (diffDays < 0) return 'text-red-700 dark:text-red-300';
                      if (diffDays <= 7) return 'text-yellow-700 dark:text-yellow-300';
                      return 'text-green-700 dark:text-green-300';
                    })()
                  }`}>
                    {(() => {
                      const now = new Date();
                      const expiry = new Date(accountDetail.expired_date);
                      const diffTime = expiry.getTime() - now.getTime();
                      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
                      
                      if (diffDays < 0) return '🚨 Segera perpanjang akun Anda';
                      if (diffDays <= 7) return '⚠️ Akun akan segera kedaluwarsa';
                      return '✅ Akun masih aktif untuk waktu yang lama';
                    })()} 
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Subscription URL Card with Accordion */}
        <div className="relative overflow-hidden bg-gradient-to-br from-cyan-50 to-blue-100 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-xl border border-cyan-200/50 dark:border-gray-700">
          <div className="absolute inset-0 bg-gradient-to-r from-cyan-500/5 to-blue-500/5"></div>
          <div className="absolute top-0 right-0 w-32 h-32 bg-cyan-400/10 rounded-full -translate-y-16 translate-x-16"></div>
          
          <div className="relative p-6 sm:p-8">
            <button
              onClick={() => setIsSubscriptionOpen(!isSubscriptionOpen)}
              className="w-full flex items-center justify-between gap-4 mb-6 group"
            >
              <div className="flex items-center gap-4">
                <div className="p-4 bg-cyan-500/10 rounded-2xl group-hover:bg-cyan-500/20 transition-colors">
                  <svg className="w-8 h-8 text-cyan-600 dark:text-cyan-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                  </svg>
                </div>
                <div className="text-left">
                  <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">URL Langganan</h3>
                  <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">Gunakan URL ini untuk mengimpor konfigurasi ke aplikasi VPN</p>
                </div>
              </div>
              <div className={`transform transition-transform duration-200 ${isSubscriptionOpen ? 'rotate-180' : ''}`}>
                <svg className="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </button>
            
            <div className={`transition-all duration-300 ease-in-out ${
              isSubscriptionOpen ? 'max-h-none opacity-100 overflow-visible' : 'max-h-0 opacity-0 overflow-hidden'
            }`}>
              <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-600/20 p-4 sm:p-6 shadow-lg">
                <div className="flex items-start gap-4">
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-3">
                      <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-sm font-semibold text-gray-700 dark:text-gray-300">Subscription URL</span>
                    </div>
                    <div className="bg-gray-100 dark:bg-gray-900 rounded-xl p-3 sm:p-4 border border-gray-300 dark:border-gray-700">
                      <p className="text-xs sm:text-sm font-mono text-gray-800 dark:text-green-400 break-all leading-relaxed">
                        {accountDetail.subscription_url}
                      </p>
                    </div>
                  </div>
                  <div className="flex-shrink-0">
                    <div className="bg-cyan-500/20 backdrop-blur-sm rounded-xl p-2 sm:p-3 border border-cyan-300/30">
                      <div className="scale-75 sm:scale-100">
                        <CopyButton text={accountDetail.subscription_url} />
                      </div>
                    </div>
                  </div>
                </div>
                
                <div className="mt-4 p-3 sm:p-4 bg-cyan-50 dark:bg-cyan-900/20 rounded-xl border border-cyan-200/50 dark:border-cyan-700/50">
                  <div className="flex items-center gap-2 text-cyan-700 dark:text-cyan-300">
                    <svg className="w-4 h-4 sm:w-5 sm:h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                    </svg>
                    <span className="text-xs sm:text-sm font-medium">Salin URL ini dan paste ke aplikasi VPN favorit Anda untuk mengimpor semua konfigurasi sekaligus</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Enhanced Connection Links with Accordion */}
        <div className="relative overflow-hidden bg-gradient-to-br from-violet-50 to-purple-100 dark:from-gray-800 dark:to-gray-900 rounded-2xl shadow-xl border border-violet-200/50 dark:border-gray-700">
          <div className="absolute inset-0 bg-gradient-to-r from-violet-500/5 to-purple-500/5"></div>
          <div className="absolute bottom-0 left-0 w-40 h-40 bg-purple-400/10 rounded-full translate-y-20 -translate-x-20"></div>
          
          <div className="relative p-6 sm:p-8">
            <button
              onClick={() => setIsConnectionsOpen(!isConnectionsOpen)}
              className="w-full flex items-center justify-between gap-4 mb-6 group"
            >
              <div className="flex items-center gap-4">
                <div className="p-4 bg-violet-500/10 rounded-2xl group-hover:bg-violet-500/20 transition-colors">
                  <svg className="w-8 h-8 text-violet-600 dark:text-violet-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0" />
                  </svg>
                </div>
                <div className="text-left">
                  <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white">Link Koneksi Individual</h3>
                  <p className="text-sm sm:text-base text-gray-600 dark:text-gray-400">Konfigurasi terpisah untuk setiap protokol yang tersedia</p>
                </div>
              </div>
              <div className={`transform transition-transform duration-200 ${isConnectionsOpen ? 'rotate-180' : ''}`}>
                <svg className="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
            </button>
            
            <div className={`transition-all duration-500 ease-in-out ${
              isConnectionsOpen
                ? 'max-h-none opacity-100 overflow-visible'
                : 'max-h-0 opacity-0 overflow-hidden'
            }`}>
              <div className="space-y-4">
                {accountDetail.connection_links
                  .filter(link => link.url && link.url.toLowerCase() !== 'false' && link.url.trim() !== '')
                  .map((link, index) => (
                  <div key={index} className="group relative">
                    <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl border border-white/20 dark:border-gray-600/20 p-4 sm:p-6 shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-[1.02]">
                      <div className="flex items-center gap-4 mb-4">
                        <div className={`p-3 rounded-xl ${
                          link.name.toLowerCase().includes('trojan') ? 'bg-purple-500/10' :
                          link.name.toLowerCase().includes('vmess') ? 'bg-blue-500/10' :
                          link.name.toLowerCase().includes('vless') ? 'bg-indigo-500/10' :
                          'bg-teal-500/10'
                        }`}>
                          <div className={`w-4 h-4 rounded-full ${
                            link.name.toLowerCase().includes('trojan') ? 'bg-purple-500' :
                            link.name.toLowerCase().includes('vmess') ? 'bg-blue-500' :
                            link.name.toLowerCase().includes('vless') ? 'bg-indigo-500' :
                            'bg-teal-500'
                          }`}></div>
                        </div>
                        <div className="flex-1">
                          <h4 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">{link.name}</h4>
                          <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Konfigurasi khusus untuk protokol {link.name.split(' ')[0]}</p>
                        </div>
                        <div className="flex-shrink-0">
                          <div className={`backdrop-blur-sm rounded-xl p-2 sm:p-3 border ${
                            link.name.toLowerCase().includes('trojan') ? 'bg-purple-500/20 border-purple-300/30' :
                            link.name.toLowerCase().includes('vmess') ? 'bg-blue-500/20 border-blue-300/30' :
                            link.name.toLowerCase().includes('vless') ? 'bg-indigo-500/20 border-indigo-300/30' :
                            'bg-teal-500/20 border-teal-300/30'
                          }`}>
                            <div className="scale-75 sm:scale-100">
                              <CopyButton text={link.url} />
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="bg-gray-100 dark:bg-gray-900 rounded-xl p-3 sm:p-4 border border-gray-300 dark:border-gray-700">
                        <p className="text-xs font-mono text-gray-800 dark:text-green-400 break-all leading-relaxed">
                          {link.url}
                        </p>
                      </div>
                      
                      <div className={`mt-4 p-3 rounded-xl border ${
                        link.name.toLowerCase().includes('trojan') ? 'bg-purple-50 dark:bg-purple-900/20 border-purple-200/50 dark:border-purple-700/50' :
                        link.name.toLowerCase().includes('vmess') ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200/50 dark:border-blue-700/50' :
                        link.name.toLowerCase().includes('vless') ? 'bg-indigo-50 dark:bg-indigo-900/20 border-indigo-200/50 dark:border-indigo-700/50' :
                        'bg-teal-50 dark:bg-teal-900/20 border-teal-200/50 dark:border-teal-700/50'
                      }`}>
                        <div className={`flex items-center gap-2 ${
                          link.name.toLowerCase().includes('trojan') ? 'text-purple-700 dark:text-purple-300' :
                          link.name.toLowerCase().includes('vmess') ? 'text-blue-700 dark:text-blue-300' :
                          link.name.toLowerCase().includes('vless') ? 'text-indigo-700 dark:text-indigo-300' :
                          'text-teal-700 dark:text-teal-300'
                        }`}>
                          <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                          <span className="text-xs sm:text-sm font-medium">Gunakan link ini untuk konfigurasi manual atau import ke aplikasi yang mendukung {link.name.split(' ')[0]}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default AccountDetailClient;