import { Announcements } from '@/components/Dashboard/Announcements';
import { TopServers } from '@/components/Dashboard/TopServers';
import Transactions from "@/components/Dashboard/Transactions";
import ServerSlideshow from '@/components/Dashboard/ServerSlideshow';
import { Metadata } from 'next';
import { UserStatsOverview } from '@/components/Dashboard/UserStatsOverview';

export const metadata: Metadata = {
  title: 'Dashboard',
  description: 'Dashboard pembelian vpn premium.',
};
export default async function Home() {

  return (
    <>
      <div className="grid grid-cols-12 gap-4 md:gap-6 2xl:gap-7.5 mb-4 md:mb-6 2xl:mb-9">
        <div className="col-span-12">
          <ServerSlideshow />
        </div>
      </div>

      <UserStatsOverview />
    


      <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-9 2xl:gap-7.5">
        <Announcements className="col-span-12 xl:col-span-8" />
        <TopServers className="col-span-12 xl:col-span-4" />
      </div>

      <div className="mt-4 grid grid-cols-12 gap-4 md:mt-6 md:gap-6 2xl:mt-9 2xl:gap-7.5">
        <div className="col-span-12">
          <Transactions />
        </div>
      </div>
    </>
  );
}
