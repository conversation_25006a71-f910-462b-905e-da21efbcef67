<!DOCTYPE html>
<html>
<head>
    <title>Test FormData Upload</title>
</head>
<body>
    <h1>Test Upload Foto</h1>
    <input type="file" id="fileInput" accept="image/*">
    <button onclick="testUpload()">Test Upload</button>
    <div id="result"></div>

    <script>
        async function testUpload() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                alert('Pilih file terlebih dahulu');
                return;
            }

            const formData = new FormData();
            formData.append('photo', file);

            try {
                const response = await fetch('http://localhost:8000/api/v1/users/me/photo', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer YOUR_TOKEN_HERE' // Ganti dengan token valid
                    },
                    body: formData
                });

                const result = await response.json();
                document.getElementById('result').innerHTML = 
                    `<pre>Status: ${response.status}\nResponse: ${JSON.stringify(result, null, 2)}</pre>`;
            } catch (error) {
                document.getElementById('result').innerHTML = 
                    `<pre>Error: ${error.message}</pre>`;
            }
        }
    </script>
</body>
</html>
