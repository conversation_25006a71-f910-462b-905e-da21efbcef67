version: '3.8'

services:
  postgres:
    image: postgres:16
    container_name: vpnshop_postgres
    restart: always
    environment:
      POSTGRES_USER: vpnshop_user
      POSTGRES_PASSWORD: vpnshop_password # Ganti dengan password yang lebih aman untuk produksi
      POSTGRES_DB: vpnshop_db
      PGTZ: 'Asia/Jakarta'
    volumes:
      # Data akan disimpan di folder ./data/postgres di dalam direktori proyek Anda
      - ./data/postgres:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U vpnshop_user -d vpnshop_db"]
      interval: 10s
      timeout: 5s
      retries: 5
